# Robot World Web API

This document describes the Web API layer for the RobotWorld application, implemented using Javalin.

## Overview

The Web API provides REST endpoints for managing the robot world and executing robot commands. It follows a clean architecture pattern where the Web API layer holds a reference to the World (Domain Layer) but the World remains unaware of the Web API implementation.

## Architecture

- **Web API Layer**: `RobotWorldApiServer` and `RobotWorldApiHandler`
- **Domain Layer**: `World`, `Robot`, `Command` classes (unchanged)
- **Database Layer**: `WorldDatabase` for persistence

## API Endpoints

### GET /world

Returns the current state of the world as JSON.

**Response Format:**
```json
{
  "width": 100,
  "height": 50,
  "obstacles": [
    {
      "x": 10,
      "y": 20,
      "width": 5,
      "height": 5,
      "type": "MOUNTAIN"
    }
  ],
  "robots": [
    {
      "name": "Tank1",
      "make": "tank",
      "x": 0,
      "y": 0,
      "direction": "NORTH",
      "shields": 10,
      "shots": 3,
      "status": "Normal"
    }
  ]
}
```

### GET /world/{name}

Restores a world from the database by name and returns its state.

**Parameters:**
- `name`: The name of the world to restore from the database

**Response:** Same format as GET /world

### POST /robot/{name}

Executes a robot command. Currently supports the `launch` command.

**Parameters:**
- `name`: The name of the robot

**Request Body:**
```json
{
  "command": "launch",
  "arguments": ["tank"]
}
```

**Supported Robot Types:**
- `tank`: 10 shields, 3 shots, 3 shot distance
- `sniper`: 5 shields, 20 shots, 5 shot distance  
- `rover`: 7 shields, 0 shots, 0 shot distance

**Response:** Standard RobotWorld JSON response format
```json
{
  "result": "OK",
  "data": {
    "message": "Done",
    "visibility": 30,
    "position": [0, 0],
    "direction": "NORTH"
  },
  "state": {
    "position": [0, 0],
    "direction": "NORTH",
    "shields": 10,
    "shots": 3,
    "status": "NORMAL"
  }
}
```

## Running the API Server

### From Command Line

```bash
# Compile the project
mvn compile

# Run the API server (default port 8080)
java -cp target/classes:target/dependency/* za.co.wethinkcode.robots.webapi.RobotWorldApiServer

# Run on custom port
java -cp target/classes:target/dependency/* za.co.wethinkcode.robots.webapi.RobotWorldApiServer 9000
```

### From IDE

Run the `main` method in `RobotWorldApiServer.java`

## Testing the API

### Using cURL

```bash
# Get world state
curl -X GET "http://localhost:8080/world"

# Launch a tank robot
curl -X POST "http://localhost:8080/robot/Tank1" \
  -H "Content-Type: application/json" \
  -d '{"command":"launch","arguments":["tank"]}'

# Launch a sniper robot
curl -X POST "http://localhost:8080/robot/Sniper1" \
  -H "Content-Type: application/json" \
  -d '{"command":"launch","arguments":["sniper"]}'
```

### Using the Demo Script

```bash
# Make the script executable
chmod +x demo_api.sh

# Run the demo
./demo_api.sh
```

### Running Unit Tests

```bash
mvn test -Dtest=RobotWorldApiTest
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Successful GET requests
- `201 Created`: Successful robot launch
- `400 Bad Request`: Invalid command or missing fields
- `404 Not Found`: World not found in database
- `500 Internal Server Error`: Server errors

## Dependencies

The Web API uses the following additional dependencies:

- **Javalin 6.1.3**: Web framework for creating REST APIs
- **org.json**: JSON parsing (already included in the project)

## Implementation Notes

1. **Separation of Concerns**: The Web API layer is completely separate from the existing socket server implementation.

2. **Thread Safety**: The API uses the existing World singleton and its thread-safe command handling mechanisms.

3. **Database Integration**: The API integrates with the existing WorldDatabase for world persistence.

4. **JSON Serialization**: Uses the existing Response.toJSONString() method for consistent response formatting.

5. **Command Execution**: Uses the existing World.execute() method with completion handlers for asynchronous command processing.

## Future Enhancements

Potential improvements for the API:

1. Support for additional robot commands (move, turn, fire, etc.)
2. WebSocket support for real-time updates
3. Authentication and authorization
4. Rate limiting
5. OpenAPI documentation
6. Docker deployment support
7. World creation and management endpoints
8. Robot state querying endpoints

## Configuration

The API server can be configured by modifying the `RobotWorldApiServer` constructor or adding configuration files. Currently, it uses the default World instance and WorldDatabase configuration.
