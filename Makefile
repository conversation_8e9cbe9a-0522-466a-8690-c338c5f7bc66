SHELL := /bin/bash
.PHONY: clean build test release tag all bump-patch bump-minor bump-major commit-and-push

# File paths
version_file = version
current_version = $(shell cat $(version_file))
dev_ver = $(current_version)-SNAPSHOT
IMAGE_NAME := gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5
TAG := $(shell git rev-parse --short HEAD)
PORT := 5000


define bump_version
$(eval MAJOR=$(shell echo $(current_version) | cut -d. -f1))
$(eval MINOR=$(shell echo $(current_version) | cut -d. -f2))
$(eval PATCH=$(shell echo $(current_version) | cut -d. -f3))
endef

# Clean target
clean:
	mvn clean

# Build target (skip tests)
build:
	mvn clean install -DskipTests

# Test target

test-unit-tests:
	@echo "Testing unit tests..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.client.* test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.commands.* test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.handlers.* test

test:
	@echo "Starting reference server..."
	@java -jar ./libs/reference-server-0.1.0.jar > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.LaunchRobotTests test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.LookCommandTests test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.GetRobotStateTests test
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 3
	@echo "Starting our server..."
	@mvn compile exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.Server > /dev/null 2>&1 & echo $$! > our_server.pid
	sleep 5
	@echo "Running full test suite (our server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.LaunchRobotTests test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.LookCommandTests test
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_tests.GetRobotStateTests test
	@kill -9 `cat our_server.pid` || true
	@rm -f our_server.pid

test-world-size-1-ref:
	@echo "Starting reference server with -s 1..."
	@java -jar ./libs/reference-server-0.2.3.jar > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.ConfigS1x1Tests test
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-world-size-2-ref:
	@echo "Starting reference server with -s 2 ..."
	@java -jar ./libs/reference-server-0.2.3.jar -s 2 > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithoutObstacleTests test
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-world-with-obstacle-ref:
	@echo "Starting reference server with -s 2 -o 1,1..."
	@nohup java -jar ./libs/reference-server-0.2.3.jar -s 2 -o 1,1 > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithObstacleTests test
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-iteration-2-ref: test-world-size-1-ref test-world-size-2-ref test-world-with-obstacle-ref

test-world-size-1:
	@echo "Starting our server with -s 1..."
	@nohup java -jar ./libs/my-server-1.1.1.jar > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.ConfigS1x1Tests test
	@echo "Killing our server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-world-size-2:
	@echo "Starting our server with -s 2 ..."
	@nohup java -jar ./libs/my-server-1.1.1.jar -s 2 > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithoutObstacleTests test
	@echo "Killing our server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-world-with-obstacle:
	@echo "Starting our server with -s 2 -o 1,1..."
	@nohup java -jar ./libs/my-server-1.1.1.jar -s 2 -o 1,1 > /dev/null 2>&1 & echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests (reference server)..."
	mvn -DfailIfNoTests=false -Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithObstacleTests test
	@echo "Killing our server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid

	sleep 5

test-iteration-2: test-world-size-1 test-world-size-2 test-world-with-obstacle

docker-test-world-with-obstacle:
	@echo "Starting our Docker container with -s 2 -o 1,1..."
	@docker run -it -d --rm --name test-server \
       -p 5000:5000 \
       gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version) \
       -s 2 -o 1,1 > container_id.txt
	sleep 5
	@echo "Running acceptance tests against Docker container..."
	mvn -DfailIfNoTests=false \
		-Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithObstacleTests \
		test
	@echo "Stopping our Docker container..."
	@docker stop `cat container_id.txt` || true
	@rm -f container_id.txt

docker-test-world-without-obstacle:
	@echo "Starting our Docker container with -s 2..."
	@docker run -it -d --rm --name test-server \
		-p 5000:5000 \
		gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version) \
		-s 2 > container_id.txt
	sleep 5
	@echo "Running acceptance tests against Docker container..."
	mvn -DfailIfNoTests=false \
		-Dtest=za.co.wethinkcode.robots.acceptance_test2.Config2x2WithoutObstacleTests \
		test
	@echo "Stopping our Docker container..."
	@docker stop `cat container_id.txt` || true
	@rm -f container_id.txt

release:
	@echo "Switching to release version..."
	sed -i "s/<version>$(dev_ver)<\/version>/<version>$(current_version)<\/version>/" pom.xml
	mvn clean package -DskipTests
	@echo "Reverting to snapshot version..."
	sed -i "s/<version>$(current_version)<\/version>/<version>$(dev_ver)<\/version>/" pom.xml
	@cp target/robot-worlds-$(current_version)-jar-with-dependencies.jar libs/my-server-$(current_version).jar
	git add libs/my-server-$(current_version).jar

# Git tagging
tag:
	git tag -a v$(current_version) -m "Release version $(current_version)"
	git push origin v$(current_version)

# All target
bump-patch:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		PATCH=$(PATCH); \
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml \
	'

# Bump minor version
bump-minor:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml \
	'

# Bump major version
bump-major:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml \
	'

commit-and-push:
	@git add .
	@git commit -m "Bump version to $(current_version)"
	@git push origin main

# Docker build target
docker-build:
	docker build -t gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version) .

docker-push:
	docker push gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version)


docker-tag:
	docker tag gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version) \
    gitlab.wethinkco.de:5050/jomorekjhb024/brownfields_robot_worlds_5:$(current_version)


prepare-artifact:
	@echo "Preparing artifact for CI..."
	@cp target/robot-worlds-$(current_version)-jar-with-dependencies.jar robot-worlds-$(current_version).jar
	@echo "Created target/robot-worlds-$(current_version).jar from target/robot-worlds-$(current_version)-jar-with-dependencies.jar"

ci-build: build prepare-artifact

ci-release: release prepare-artifact


test-all: test-unit-tests test test-iteration-2-ref test-iteration-2

all: clean test-all build release

publish-patch: bump-patch release commit-and-push tag

publish-minor: bump-minor release commit-and-push tag

publish-major: bump-major release commit-and-push tag