Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
Missing required parameter for option '--size' (<size>)
Usage: robots [-hV] [-m=<mines>] [-o=<obstacle>] [-p=<port>] [-pt=<pits>]
              [-s=<size>] [-v=<visibility>]
Starts the Robots World server.
  -h, --help              Show this help message and exit.
  -m, --mine=<mines>      Position of fixed mine as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -o, --obstacle=<obstacle>
                          Position of fixed obstacle as [x,y] coordinate in
                            form 'x,y', or 'none' or 'random'
  -p, --port=<port>       Port to listen for client connections
      -pt, --pit=<pits>   Position of fixed pit as [x,y] coordinate in form 'x,
                            y', or 'none' or 'random'
  -s, --size=<size>       Size of the world as one side of a square grid
  -v, --visibility=<visibility>
                          Visibility for robot in nr of steps
  -V, --version           Print version information and exit.
