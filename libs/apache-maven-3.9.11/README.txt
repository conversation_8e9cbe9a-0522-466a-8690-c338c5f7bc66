
                          Apache Maven

  What is it?
  -----------

  Maven is a software project management and comprehension tool. Based on
  the concept of a Project Object Model (POM), <PERSON><PERSON> can manage a project's
  build, reporting and documentation from a central piece of information.

  Documentation
  -------------

  The most up-to-date documentation can be found at https://maven.apache.org/.

  Release Notes
  -------------

  The full list of changes, system requirements and related can be found at https://maven.apache.org/docs/history.html.

  Installing Maven
  ----------------

  For complete documentation see https://maven.apache.org/download.html#Installation

  Licensing
  ---------

  Please see the file called LICENSE.

  Maven URLS
  ----------

  Home Page:          https://maven.apache.org/
  Downloads:          https://maven.apache.org/download.html
  Release Notes:      https://maven.apache.org/docs/history.html
  Mailing Lists:      https://maven.apache.org/mailing-lists.html
  Source Code:        https://gitbox.apache.org/repos/asf/maven.git
  Issue Tracking:     https://issues.apache.org/jira/browse/MNG
  Wiki:               https://cwiki.apache.org/confluence/display/MAVEN/
  Available Plugins:  https://maven.apache.org/plugins/
