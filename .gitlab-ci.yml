image: eclipse-temurin:21-jdk

stages:
  - build
  - test
  - release
  - docker_build

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  GIT_STRATEGY: clone

cache:
  paths:
    - .m2/repository

before_script:
  - apt-get update && apt-get install -y git make maven
  - export SHELL=/bin/bash
  - java -version
  - mvn -version


test:
  stage: test
  script:
    - make test

build:
  stage: build
  script:
    - make build
  artifacts:
    paths:
      - target/*.jar


release:
  stage: release
  script:
    - make release
  artifacts:
    paths:
      - libs/my-server-*.jar
  only:
    - main

publish-patch:
  stage: release
  before_script:
    - apt-get update
    - apt-get install -y git make maven openjdk-21-jdk
    - export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
    - export PATH=$JAVA_HOME/bin:$PATH
    - git config --global user.name "Mbali Sithole"
    - git config --global user.email "<EMAIL>"
  script:
    - make publish-patch
  only:
    - main
  when: manual

publish-minor:
  stage: release
  before_script:
    - apt-get update
    - apt-get install -y git make maven openjdk-21-jdk
    - export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
    - export PATH=$JAVA_HOME/bin:$PATH
    - git config --global user.name "Mbali Sithole"
    - git config --global user.email "<EMAIL>"
  script:
    - make publish-minor
  only:
    - main
  when: manual

publish-major:
  stage: release
  before_script:
    - apt-get update
    - apt-get install -y git make maven openjdk-21-jdk
    - export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
    - export PATH=$JAVA_HOME/bin:$PATH
    - git config --global user.name "Mbali Sithole"
    - git config --global user.email "<EMAIL>"
  script:
    - make publish-major
  only:
    - main
  when: manual

docker_build:
  stage: docker_build
  image: docker:stable
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - apk add --no-cache make bash
    - echo "$CI_JOB_TOKEN" | docker login -u gitlab-ci-token --password-stdin $CI_REGISTRY
  script:
    - make docker-build
    - make docker-push
  only:
    - main
