FROM maven:3.9.6-eclipse-temurin-21

LABEL maintainers="JOHN <<EMAIL>>, MBALI <<EMAIL>>, THA<PERSON> <<EMAIL>>"

RUN apt-get update && \
    apt-get install -y git

RUN git config --global user.name "CI_BOT" && \
    git config --global user.email "<EMAIL>"

WORKDIR /app/
COPY . /app/
COPY libs/my-server-2.0.1.jar /app/robot-worlds-2.0.1.jar

EXPOSE 5050

ENTRYPOINT ["java", "-jar", "/app/robot-worlds-2.0.1.jar"]

CMD ["-p", "5050"]
