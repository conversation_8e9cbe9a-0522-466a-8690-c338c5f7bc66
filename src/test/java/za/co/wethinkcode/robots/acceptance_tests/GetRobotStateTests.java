package za.co.wethinkcode.robots.acceptance_tests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a client
 * I want to get the current state of my robot
 * So that I can know its position, shield strength, shots, direction and status
 */
class GetRobotStateTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void validStateRequestShouldSucceed() {
        // Given I am connected to the Robot World server
        assertTrue(serverClient.isConnected());

        // And I have a robot named "<PERSON><PERSON><PERSON>" in the world
        String launchRequest = "{" +
                "  \"robot\": \"Bond\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);

        assertEquals("OK", launchResponse.get("result").asText());


        // When I send a state request for "Bond"
        String stateRequest = "{" +
                "  \"robot\": \"Bond\"," +
                "  \"command\": \"state\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(stateRequest);

        // Then the response should be "OK"
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // And I should receive a state object containing all required properties
        JsonNode state = response.get("state");
        assertNotNull(state);
        
        // Check position coordinates
        assertNotNull(state.get("position"));
        assertTrue(state.get("position").isArray());
        assertTrue(state.get("position").get(0).isNumber());
        assertTrue(state.get("position").get(1).isNumber());

        // Check direction
        assertNotNull(state.get("direction"));
        String direction = state.get("direction").asText();
        assertTrue(direction.matches("NORTH|SOUTH|EAST|WEST"));

        // Check shields
        assertNotNull(state.get("shields"));
        assertTrue(state.get("shields").isNumber());
        assertTrue(state.get("shields").asInt() >= 0);

        // Check shots
        assertNotNull(state.get("shots"));
        assertTrue(state.get("shots").isNumber());
        assertTrue(state.get("shots").asInt() >= 0);
        // Check status
        assertNotNull(state.get("status"));

        String disconnect = "{" +
                "  \"robot\": \"Bond\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }

    @Test
    void invalidRobotStateShouldFail() {
        // Given I am connected to the Robot World server
        assertTrue(serverClient.isConnected());

        // When I send a state request for a non-existent robot
        String request = "{" +
                "  \"robot\": \"UNKNOWN-ROBOT\"," +
                "  \"command\": \"state\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then the response should be "ERROR"
        assertNotNull(response.get("result"));
        assertEquals("ERROR", response.get("result").asText());

        // And I should receive the message "Robot does not exist"
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertEquals("Robot does not exist", response.get("data").get("message").asText());
    }
}