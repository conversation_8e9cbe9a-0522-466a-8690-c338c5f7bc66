package za.co.wethinkcode.robots.acceptance_tests;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.*;
import java.net.Socket;

public class RobotWorldJsonClient implements RobotWorldClient {

    private Socket socket;
    @Override
    public void connect(String defaultIp, int defaultPort) {
        try {
            socket = new Socket(defaultIp, defaultPort);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void disconnect() {
        try {
            socket.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public boolean isConnected() {
        return socket.isConnected();
    }

    @Override
    public JsonNode sendRequest(String request) {
        try {
            BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream()));
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            bufferedWriter.write(request);
            bufferedWriter.newLine();
            bufferedWriter.flush();
            String response = bufferedReader.readLine();
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readTree(response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}
