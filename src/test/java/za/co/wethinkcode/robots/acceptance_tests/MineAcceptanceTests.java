package za.co.wethinkcode.robots.acceptance_tests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Acceptance tests for mine placement and effects in Robot Worlds.
 */
class MineAcceptanceTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
        assertTrue(serverClient.isConnected(), "Server should be running before tests.");
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    private JsonNode send(String json) {
        return serverClient.sendRequest(json);
    }

    private void disconnectRobot(String name) {
        send("{\"robot\": \"" + name + "\", \"command\": \"disconnect\", \"arguments\": []}");
    }

    @Test
    void steppingOnOwnMineReducesShields() {

        String request = "{" +
                "  \"robot\": \"Miner\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"rover\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);
        System.out.println(response.toString());

        String request2 = "{" +
                "  \"robot\": \"Miner\"," +
                "  \"command\": \"Mine\"," +
                "  \"arguments\": []" +
                "}";

        JsonNode response2 = serverClient.sendRequest(request2);
        System.out.println(response2.toString());


        String request3 = "{" +
                "  \"robot\": \"Miner\"," +
                "  \"command\": \"back\"," +
                "  \"arguments\": [\"1\"]" +
                "}";

        JsonNode response3 = serverClient.sendRequest(request3);
        int shield = response3.get("state").get("shields").asInt() - 3;
        System.out.println(response3.get("state").toString());
        assertNotNull(response.get("result"));

        // And the position should be (x:0, y:0)
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data"));
        assertEquals(4, shield);
        serverClient.disconnect();

    }


    @Test
    void cannotPlaceMineIfNotRover() {
        send("{\"robot\": \"ShooterBot\", \"command\": \"launch\", \"arguments\": [\"shooter\",\"5\",\"5\"]}");

        JsonNode response = send("{\"robot\": \"ShooterBot\", \"command\": \"mine\", \"arguments\": []}");
        assertEquals("ERROR", response.get("result").asText());
        assertTrue(response.get("data").get("message").asText().contains("Only rovers"),
                "Error message should explain that only rovers can place mines.");

        disconnectRobot("ShooterBot");
    }

    @Test
    void placingMineChangesStatus() {
        send("{\"robot\": \"Rover\", \"command\": \"launch\", \"arguments\": [\"rover\",\"5\",\"5\"]}");

        send("{\"robot\": \"Rover\", \"command\": \"mine\", \"arguments\": []}");
        // Check state right after placement
        JsonNode duringPlacement = send("{\"robot\": \"Rover\", \"command\": \"state\", \"arguments\": []}");
        String status = duringPlacement.get("state").get("status").asText();
        assertTrue(status.equalsIgnoreCase("PLACING_MINE") || status.equalsIgnoreCase("NORMAL"),
                "Status should reflect mine placement in progress.");

        disconnectRobot("Rover");
    }

    @Test
    void mineDetectionWithinVisibilityRange() {
        send("{\"robot\": \"Seeker\", \"command\": \"launch\", \"arguments\": [\"rover\",\"5\",\"5\"]}");
        send("{\"robot\": \"Placer\", \"command\": \"launch\", \"arguments\": [\"rover\",\"5\",\"5\"]}");

        // Place mine within detection range
        send("{\"robot\": \"Placer\", \"command\": \"mine\", \"arguments\": []}");

        JsonNode state = send("{\"robot\": \"Seeker\", \"command\": \"state\", \"arguments\": []}");
        // Assuming server exposes mines in "visibleMines" list inside state
        if (state.get("state").has("visibleMines")) {
            boolean seesMine = state.get("state").get("visibleMines").size() > 0;
            assertTrue(seesMine, "Mine within detection range should be visible.");
        }

        disconnectRobot("Seeker");
        disconnectRobot("Placer");
    }
}
