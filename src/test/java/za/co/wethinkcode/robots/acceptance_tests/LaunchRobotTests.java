package za.co.wethinkcode.robots.acceptance_tests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.domain.world.World;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player
 * I want to launch my robot in the online robot world
 * So that I can break the record for the most robot kills
 */
class LaunchRobotTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void validLaunchShouldSucceed(){
        // Given that I am connected to a running Robot Worlds server
        // And the world is of size 1x1 (The world is configured or hardcoded to this size)
        assertTrue(serverClient.isConnected());

        // When I send a valid launch request to the server
        String request = "{" +
                "  \"robot\": \"John\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get a valid response from the server
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // And the position should be (x:0, y:0)
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("position"));

        // And I should also get the state of the robot
        assertNotNull(response.get("state"));

        String disconnect = "{" +
                "  \"robot\": \"John\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);

    }

    @Test
    void invalidLaunchShouldFail(){
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // When I send a invalid launch request with the command "luanch" instead of "launch"
        String request = "{" +
                "\"robot\": \"Thato\"," +
                "\"command\": \"luanch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get an error response
        assertNotNull(response.get("result"));
        assertEquals("ERROR", response.get("result").asText());

        // And the message "Unsupported command"
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertTrue(response.get("data").get("message").asText().contains("Unsupported command"));

        String disconnect = "{" +
                "  \"robot\": \"Thato\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }

    @Test
    void worldAtMaximumCapacityShouldFail() {
//        connectToServer();
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // And the world is at maximum capacity with robots
        // First, fill the world with robots
        // Assuming max capacity is 4
        for (int i = 0; i < 9; i++) {
            String robotName = "Robot" + i;
            String request = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            serverClient.sendRequest(request);
        }
        // When I send a valid launch request
        String requestTwo = "{" +
                "\"robot\": \"OneMoreRobot3\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(requestTwo);
        // Then I should get an "ERROR" response
        assertNotNull(response.get("result"));
        System.out.println(response);
        assertEquals("ERROR", response.get("result").asText());

        // And the message should be "Too many of you in this world"
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
       // assertEquals("Too many of you in this world", response.get("data").get("message").asText());
        for (int i = 0; i < 9; i++) {
            String robotName = "Robot" + i;
            String request = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            serverClient.sendRequest(request);
        }
    }

    @Test
    void duplicateRobotNameShouldFail() {
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // And there is already a robot named "Mbali" in the world
        String firstLaunch = "{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(firstLaunch);

        // When I send a launch request with name "Mbali"
        String duplicateRequest = "{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(duplicateRequest);

        // Then I should get an "ERROR" response
        assertNotNull(response.get("result"));
        assertEquals("ERROR", response.get("result").asText());

        // And the message should be "Too many of you in this world"
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertEquals("Too many of you in this world", response.get("data").get("message").asText());

        String disconnect = "{" +
                "  \"robot\": \"Mbali\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);

    }
}
