package za.co.wethinkcode.robots.acceptance_tests;

import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.infrastructure.Server;

import java.io.*;
import java.util.concurrent.*;

import static org.junit.jupiter.api.Assertions.*;

class AdminConsoleAcceptanceTests {
    private static final int DEFAULT_PORT = 5000;
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient client = new RobotWorldJsonClient();
    private static ExecutorService executor;

    @BeforeAll
    static void startServer() throws Exception {
        executor = Executors.newSingleThreadExecutor();
        executor.submit(() -> Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "1"}));
        Thread.sleep(500); // give server time to boot
    }

    @AfterAll
    static void stopServer() {
        executor.shutdownNow();
    }

    @BeforeEach
    void connect() {
        client.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnect() {
        client.disconnect();
    }

    private String runAdminCommands(String commands) throws Exception {
        ByteArrayOutputStream consoleOutput = new ByteArrayOutputStream();
        PrintStream originalOut = System.out;
        InputStream originalIn = System.in;

        try {
            System.setOut(new PrintStream(consoleOutput));
            System.setIn(new ByteArrayInputStream(commands.getBytes()));

            // Run in background so it doesn't block tests
            CompletableFuture.runAsync(() -> {
                try {
                    var method = Server.class.getDeclaredMethod("startAdminConsole",
                            za.co.wethinkcode.robots.domain.world.World.class);
                    method.setAccessible(true);
                    method.invoke(null, za.co.wethinkcode.robots.domain.world.World.getInstance());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            Thread.sleep(300); // give time for commands to run
            return consoleOutput.toString();
        } finally {
            System.setOut(originalOut);
            System.setIn(originalIn);
        }
    }

    @Test
    void robotsCommandShouldListRobots() throws Exception {
        // Launch a robot first
        String launchRequest = "{" +
                "\"robot\": \"Optimus\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        client.sendRequest(launchRequest);

        String output = runAdminCommands("robots\nquit\n");
        assertTrue(output.contains("Optimus"), "robots output should list the launched robot");
    }

    @Test
    void dumpCommandShouldShowWorldState() throws Exception {
        String output = runAdminCommands("dump\nquit\n");
        assertTrue(output.toLowerCase().contains("world"), "dump output should contain 'world'");
    }

    @Test
    void displayCommandShouldPrintMap() throws Exception {
        String output = runAdminCommands("display\nquit\n");
        assertFalse(output.trim().isEmpty(), "display should print something");
    }

    @Test
    void purgeCommandShouldRemoveRobot() throws Exception {
        // Launch a robot to be purged
        String launchRequest = "{" +
                "\"robot\": \"Bumblebee\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        client.sendRequest(launchRequest);

        String output = runAdminCommands("purge bumblebee\nquit\n");

        // Try to move purged robot — should fail
        String moveRequest = "{" +
                "\"robot\": \"Bumblebee\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"1\"]" +
                "}";
        var moveResponse = client.sendRequest(moveRequest);
        assertEquals("ERROR", moveResponse.get("result").asText(), "Purged robot should not exist anymore");

        assertTrue(output.toLowerCase().contains("purged"), "purge output should mention purge");
    }

    @Test
    void quitCommandShouldStopServer() throws Exception {
        runAdminCommands("quit\n");
        // This won't always kill the thread immediately in async test, but we can at least check server stops accepting
        client.disconnect();
        assertThrows(Exception.class, () -> client.connect(DEFAULT_IP, DEFAULT_PORT));
    }
}

