package za.co.wethinkcode.robots.acceptance_tests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class LookCommandTests {

    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    public static final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    public void testLookEmptyWorld() {
        assertTrue(serverClient.isConnected());

        String launchRequest = "{" +
                "\"robot\": \"Thato\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"tank\",\"3\",\"1\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response);

        String lookRequest = "{" +
                "\"robot\": \"Thato\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";

        response = serverClient.sendRequest(lookRequest);

        assertNotNull(response.get("data"));
        assertNotNull(response.get("state"));

        int count = 0;
        for (JsonNode obj : response.get("data").get("objects")) {
            if ("EDGE".equals(obj.get("type").asText())) {
                count++;
            }
        }

        assertEquals(4, count);
        String disconnect = "{" +
                "  \"robot\": \"Thato\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }
}
