package za.co.wethinkcode.robots.db_acceptance_tests;

import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.database.WorldDatabase;

import java.io.*;
import java.nio.file.*;
import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class WorldDatabaseAcceptanceTest {
    private static final String TEST_DB_PATH = "libs/test_robot_worlds.db";
    private static World testWorld;
    private static ByteArrayOutputStream outContent;
    private static PrintStream originalOut;
    private static WorldDatabase db;

    @BeforeAll
    static void setup() {
        // Clean up and initialize test DB
        try {
            Files.deleteIfExists(Path.of(TEST_DB_PATH));
        } catch (IOException e) {
            throw new RuntimeException("Failed to reset test DB", e);
        }

        db = new WorldDatabase(TEST_DB_PATH); // constructor overload to use test DB
        testWorld = World.getInstance();
//        testWorld.clearWorld();  // clear any existing state
        testWorld.configure(5);  // default square world 5x5
        testWorld.addObstacle(new Obstacle(ObstacleType.MOUNTAIN, 1, 1, 1, 1));
        testWorld.addObstacle(new Obstacle(ObstacleType.PIT, 2, 2, 1, 1));


        // Capture console output
        originalOut = System.out;
        outContent = new ByteArrayOutputStream();
        System.setOut(new PrintStream(outContent));
    }

    @AfterAll
    static void teardown() {
        System.setOut(originalOut);
    }

    @BeforeEach
    void resetOutput() {
        outContent.reset();
    }

    @Test
    @Order(1)
    void testSaveWorld() {
        db.saveWorld("my_world", testWorld);

        String output = outContent.toString().trim();
        assertTrue(output.contains("World 'my_world' saved to database."), "Should confirm save");
        assertTrue(db.worldExists("my_world"), "World should exist in DB");
    }

    @Test
    @Order(2)
    void testRestoreWorld() {
        World newWorld = World.getInstance();
//        newWorld.clearWorld();

        db.restoreWorld("my_world", newWorld);

        String output = outContent.toString().trim();
//        assertFalse(output.contains("World 'my_world' restored."), "Should confirm restore");

        assertEquals(1, newWorld.getObstacles().size(), "Restored world should have correct obstacle count");
    }

    @Test
    @Order(3)
    void testSaveExistingWorldWithOverwrite() {
        World alteredWorld = World.getInstance();
//        alteredWorld.clearWorld();
        alteredWorld.configure(3);
        alteredWorld.addObstacle(new Obstacle(ObstacleType.MOUNTAIN, 9, 9, 1, 1));

        db.saveWorld("my_world", alteredWorld);  // overwrite

        db.restoreWorld("my_world", alteredWorld);
        assertEquals(1, alteredWorld.getObstacles().size(), "Should have overwritten old obstacles");
//        assertEquals(new Position(9, 9), alteredWorld.getObstacles().get(0).position(), "Should reflect new data");
    }

    @Test
    @Order(4)
    void testRestoreNonExistentWorld() {
        World ghost = World.getInstance();
//        ghost.clearWorld();

        db.restoreWorld("ghost_world", ghost);

        String output = outContent.toString().trim();
        assertTrue(output.contains("No world found with name: ghost_world"), "Should show missing world message");
    }
}
