package za.co.wethinkcode.robots.server;

import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.domain.responses.Response;

import static org.junit.jupiter.api.Assertions.*;


public class ResponseTest {

    @Test
    public void testOkResponse() {
        JSONObject data = new JSONObject();
        data.put("key", "value");

        Response response = Response.ok(data, "Request successful");


        assertTrue(response.object.has("result"), "Response should contain 'result' key.");
        assertEquals("OK", response.object.getString("result")); // Ensure this matches actual output

        assertFalse(response.object.has("message"), "Response should contain 'message' key.");
        assertEquals("value", response.object.getJSONObject("data").get("key"));

        assertTrue(response.object.has("data"), "Response should contain 'data' key.");
        assertEquals(data.toString(), response.object.getJSONObject("data").toString());
    }

    @Test
    public void testErrorResponse() {
        Response response = new Response("ERROR", "An error occurred");

        assertEquals("ERROR", response.object.getString("result"));
        assertTrue(response.object.has("data"), "Error response should have data.");
        assertEquals("An error occurred", response.object.getJSONObject("data").getString("message"));
    }

    @Test
    public void testEmptyOkResponse() {
        Response response = Response.ok(new JSONObject(), "No data");

        assertEquals("OK", response.object.getString("result"));
        assertFalse(response.object.getJSONObject("data").has("message"));
        assertTrue(response.object.has("data"));
        assertTrue(response.object.getJSONObject("data").isEmpty(), "Data should be empty.");
    }

    @Test
    public void testMessageHandling() {
        Response response = new Response("OK", "Test message");

        assertEquals("Test message", response.object.getJSONObject("data").getString("message"));
        assertEquals("OK", response.object.getString("result"));
    }
}