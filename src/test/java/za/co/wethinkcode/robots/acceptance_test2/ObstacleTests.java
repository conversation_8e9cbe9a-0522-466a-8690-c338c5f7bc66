package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;
import za.co.wethinkcode.robots.domain.commands.LaunchCommand;
import za.co.wethinkcode.robots.domain.commands.LookCommand;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.world.World;

import static org.junit.jupiter.api.Assertions.*;

public class ObstacleTests {

    // unit tests that don't require server connection
    @Test
    public void testLookDetectsObstacleInSmallWorld() {
        // Given a world of size 4x4 (coordinates from [-2,-2] to [2,2])
        World world = new World(4, 4);

        // And the world has an obstacle at coordinate [0,1]
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 0, 1, 1, 1);
        assertTrue(world.addObstacle(obstacle), "Should be able to add obstacle at [0,1]");

        //and I have successfully launched a robot into the world (robot will be placed at [0,0] by default)
        Robot robot = new Robot("TestRobot", "tank");

        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});
        world.execute(launchCommand, "test-client", response -> {
            assertTrue(response.isOKResponse(), "Robot should launch successfully");

            //  when I ask the robot to look
            LookCommand lookCommand = new LookCommand(robot, new String[]{});
            world.execute(lookCommand, "test-client", lookResponse -> {
                // Then I should get a response back with an object of type OBSTACLE at a distance of 1 step
                assertTrue(lookResponse.isOKResponse(), "Look command should succeed");
                assertNotNull(lookResponse.object.get("data"), "Response should contain data");

                JSONObject data = lookResponse.object.getJSONObject("data");
                JSONArray objects = data.getJSONArray("objects");
                assertNotNull(objects, "Response should contain objects array");

                boolean foundObstacle = false;
                for (int i = 0; i < objects.length(); i++) {
                    JSONObject obj = objects.getJSONObject(i);
                    if ("OBSTACLE".equals(obj.getString("type"))) {
                        assertEquals(1, obj.getInt("distance"), "Obstacle should be at distance 1");
                        foundObstacle = true;
                        break;
                    }
                }

                assertTrue(foundObstacle, "Should find an obstacle in the look response");
            });
        });
    }

    @Test
    public void testLookDetectsObstacleIn2x2World() {
        //given a world of size 2x2 (coordinates from[-1,-1] to [1,1])
        World world = new World(2, 2);

        //and the world has an obstacle at coordinate [-1,0] (since [0,1] would be out of bounds)
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, -1, 0, 1, 1);
        assertTrue(world.addObstacle(obstacle), "Should be able to add obstacle at [-1,0]");

        // and I have successfully launched a robot into the world (robot will be placed at [0,0] by default)
        Robot robot = new Robot("TestRobot", "tank");

        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});
        world.execute(launchCommand, "test-client", response -> {
            assertTrue(response.isOKResponse(), "Robot should launch successfully");

            //when I ask the robot to look
            LookCommand lookCommand = new LookCommand(robot, new String[]{});
            world.execute(lookCommand, "test-client", lookResponse -> {
                // when I should get a response back with an object of type OBSTACLE at a distance of 1 step
                assertTrue(lookResponse.isOKResponse(), "Look command should succeed");
                assertNotNull(lookResponse.object.get("data"), "Response should contain data");

                JSONObject data = lookResponse.object.getJSONObject("data");
                JSONArray objects = data.getJSONArray("objects");
                assertNotNull(objects, "Response should contain objects array");

                boolean foundObstacle = true;
                for (int i = 0; i < objects.length(); i++) {
                    JSONObject obj = objects.getJSONObject(i);
                    if ("OBSTACLE".equals(obj.getString("type"))) {
                        assertEquals(1, obj.getInt("distance"), "Obstacle should be at distance 1");
                        foundObstacle = true;
                        break;
                    }
                }

                assertTrue(foundObstacle, "Should find an obstacle in the look response");
            });
        });
    }

    @Test
    public void testLookDetectsObstacleViaClientServer() {
        // this test requires a running server, so we'll skip it if no server is available
        final int DEFAULT_PORT = 5000;
        final String DEFAULT_IP = "localhost";
        final RobotWorldClient serverClient = new RobotWorldJsonClient();

        try {
            // try to connect to the server
            serverClient.connect(DEFAULT_IP, DEFAULT_PORT);

            // given that I am connected to a running Robot Worlds server
            assertTrue(serverClient.isConnected());

            //when I send a valid launch request to the server
            String launchRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"tank\",\"5\",\"5\"]" +
                    "}";
            JsonNode launchResponse = serverClient.sendRequest(launchRequest);
            assertNotNull(launchResponse);
            assertEquals("OK", launchResponse.get("result").asText());

            //and I send a look request
            String lookRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"look\"," +
                    "\"arguments\": []" +
                    "}";
            JsonNode lookResponse = serverClient.sendRequest(lookRequest);

            // then I should get a response back
            assertNotNull(lookResponse.get("data"));
            assertNotNull(lookResponse.get("state"));

            // and the response should contain objects (edges and possibly obstacles)
            JsonNode objects = lookResponse.get("data").get("objects");
            assertNotNull(objects);
            assertTrue(objects.size() > 0, "Should find at least some objects (edges) in the look response");

            // clean up
            String disconnectRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}";
            serverClient.sendRequest(disconnectRequest);

        } catch (Exception e) {
            //if server is not running, skip this test
            System.out.println("Skipping client-server test - server not available: " + e.getMessage());
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "Server not available for client-server test");
        } finally {
            try {
                serverClient.disconnect();
            } catch (Exception e) {
                //ignore disconnect errors
            }
        }
    }
}


