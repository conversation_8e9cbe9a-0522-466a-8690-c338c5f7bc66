package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

public class LaunchTests {
    private static final int DEFAULT_PORT = 5000;
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    public void canLaunchAnotherRobot() {
        assertTrue(serverClient.isConnected());

        String firstRobotRequest = "{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode firstResponse = serverClient.sendRequest(firstRobotRequest);
        assertEquals("OK", firstResponse.get("result").asText());

        String secondRobotRequest = "{" +
                "\"robot\": \"John\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode secondResponse = serverClient.sendRequest(secondRobotRequest);

        assertEquals("OK", secondResponse.get("result").asText());
        assertNotNull(secondResponse.get("state"));

        serverClient.sendRequest("{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"disconnect\"," +
                "\"arguments\": []" +
                "}");
        serverClient.sendRequest("{" +
                "\"robot\": \"John\"," +
                "\"command\": \"disconnect\"," +
                "\"arguments\": []" +
                "}");

    }
}
