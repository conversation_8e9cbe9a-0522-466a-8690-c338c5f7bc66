package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class Config2x2WithObstacleTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    public void testRobotsNeverOccupyObstaclePosition() {
        assertTrue(serverClient.isConnected());

        // Launch 8 robots and ensure none end up at [1,1]
        for (int i = 0; i < 8; i++) {
            String launch = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode resp = serverClient.sendRequest(launch);
            assertEquals("OK", resp.get("result").asText());

            // Position comes back as array under state.position: [x, y]
            JsonNode pos = resp.get("state").get("position");
            int x = pos.get(0).asInt();
            int y = pos.get(1).asInt();
            assertFalse(x == 1 && y == 1, "Robot launched on obstacle coordinate [1,1]");
        }

        // Clean‑up
        for (int i = 0; i < 8; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }
    @Test
    public void testWorldIsFullAfter8Robots() {
        assertTrue(serverClient.isConnected());

        // Launch 8 robots successfully
        for (int i = 0; i < 8;i++) {
            String launchRequest = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            if( response.get("result").equals("ERROR")) break;
            assertEquals("OK", response.get("result").asText());
        }

        //9th robot cant launch due to space limit
        String extraRobotRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRobotRequest);

        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                response.get("data").get("message").asText());

        // Disconnect all robots
        for (int i = 0; i < 8; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }


    @Test
    public void testLookDetectsObstacle() {
        // this test requires a running server, so we'll skip it if no server is available
        final int DEFAULT_PORT = 5000;
        final String DEFAULT_IP = "localhost";
        final RobotWorldClient serverClient = new RobotWorldJsonClient();

        try {
            // try to connect to the server
            serverClient.connect(DEFAULT_IP, DEFAULT_PORT);

            // given that I am connected to a running Robot Worlds server
            assertTrue(serverClient.isConnected());

            //when I send a valid launch request to the server
            String launchRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"tank\",\"5\",\"5\"]" +
                    "}";
            JsonNode launchResponse = serverClient.sendRequest(launchRequest);
            assertNotNull(launchResponse);
            assertEquals("OK", launchResponse.get("result").asText());

            //and I send a look request
            String lookRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"look\"," +
                    "\"arguments\": []" +
                    "}";
            JsonNode lookResponse = serverClient.sendRequest(lookRequest);

            // then I should get a response back
            assertNotNull(lookResponse.get("data"));
            assertNotNull(lookResponse.get("state"));

            // and the response should contain objects (edges and possibly obstacles)
            JsonNode objects = lookResponse.get("data").get("objects");
            assertNotNull(objects);
            assertTrue(objects.size() > 0, "Should find at least some objects (edges) in the look response");

            // clean up
            String disconnectRequest = "{" +
                    "\"robot\": \"TestBot\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}";
            serverClient.sendRequest(disconnectRequest);

        } catch (Exception e) {
            //if server is not running, skip this test
            System.out.println("Skipping client-server test - server not available: " + e.getMessage());
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "Server not available for client-server test");
        } finally {
            try {
                serverClient.disconnect();
            } catch (Exception e) {
                //ignore disconnect errors
            }
        }
    }
    @Test
    public void testLookDetectsObstacleAndRobotsIn2x2World() {
        try {
            //given that I am connected to a running Robot Worlds server
            assertTrue(serverClient.isConnected());

            // launched multiple robots into the world
            //launch 8 robots to fill up the 2x 2 world  (9 positions - 1 obstacle = 8 available positions)
            for (int i = 0; i < 8; i++) {
                String launchRequest = "{" +
                        "\"robot\": \"Robot" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"tank\",\"5\",\"5\"]" +
                        "}";
                JsonNode launchResponse = serverClient.sendRequest(launchRequest);
                assertNotNull(launchResponse);
                assertEquals("OK", launchResponse.get("result").asText(),
                    "Robot" + i + " should launch successfully");
            }

            // ask the first robot to look around
            String lookRequest = "{" +
                    "\"robot\": \"Robot0\"," +
                    "\"command\": \"look\"," +
                    "\"arguments\": []" +
                    "}";
            JsonNode lookResponse = serverClient.sendRequest(lookRequest);

            // should get a successful response back
            assertNotNull(lookResponse);
            assertEquals("OK", lookResponse.get("result").asText(), "Look command should succeed");

            //response should contain data about visible objects
            assertNotNull(lookResponse.get("data"), "Response should contain data");
            assertNotNull(lookResponse.get("state"), "Response should contain state");

            JsonNode objects = lookResponse.get("data").get("objects");
            assertNotNull(objects, "Response should contain objects array");
            assertTrue(objects.size() > 0, "Should find at least some objects in the look response");

            //  should be able to see obstacles and other robots within visibility range
            boolean foundObstacle = false;
            int robotCount = 0;
            int edgeCount = 0;

            for (JsonNode obj : objects) {
                String type = obj.get("type").asText();
                int distance = obj.get("distance").asInt();

                if ("OBSTACLE".equals(type)) {
                    foundObstacle = true;
                    assertTrue(distance >= 1, "Obstacle should be at distance 1 or more");
                } else if ("ROBOT".equals(type)) {
                    robotCount++;
                    assertTrue(distance >= 1, "Robot should be at distance 1 or more");
                } else if ("EDGE".equals(type)) {
                    edgeCount++;
                }
            }

            // in a 2x2 world with obstacles and multiple robots we should see some objects
            assertTrue(objects.size() > 0, "Should detect some objects (edges, obstacles, or robots)");
            assertTrue(edgeCount > 0, "Should always see world edges");

            //clean up - disconnect all robots
            for (int i = 0; i < 8; i++) {
                String disconnectRequest = "{" +
                        "\"robot\": \"Robot" + i + "\"," +
                        "\"command\": \"disconnect\"," +
                        "\"arguments\": []" +
                        "}";
                serverClient.sendRequest(disconnectRequest);
            }

        } catch (Exception e) {
            //if server is not running, skip this test
            System.out.println("Skipping client-server test - server not available: " + e.getMessage());
            org.junit.jupiter.api.Assumptions.assumeTrue(false, "Server not available for client-server test");
        }
    }
}
