package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Scenario: Launch robots into a world with an obstacle at [1,1]

 * Assumes the reference server is running with:
 *   java -jar reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1
 */
public class  LaunchRobotsIntoWorldWithObstacleTests {
    private static final int DEFAULT_PORT = 5000;
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connect() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnect() {
        serverClient.disconnect();
    }

    @Test
    public void testRobotsNeverOccupyObstaclePosition() {
        assertTrue(serverClient.isConnected());

        // Launch 8 robots and ensure none end up at [1,1]
        for (int i = 0; i < 8; i++) {
            String launch = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode resp = serverClient.sendRequest(launch);
            assertEquals("OK", resp.get("result").asText());

            // Position comes back as array under state.position: [x, y]
            JsonNode pos = resp.get("state").get("position");
            int x = pos.get(0).asInt();
            int y = pos.get(1).asInt();
            assertFalse(x == 1 && y == 1, "Robot launched on obstacle coordinate [1,1]");
        }

        // Clean‑up
        for (int i = 0; i < 8; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }
}
