package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a client
 * I want to move my robot in different directions
 * So that I can navigate through the world and complete objectives
 */
class MovementAtWorldBoundaryTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    /*@Test
    void successfulMovementInClearPath() {
        // Given I have a robot at position (x:2, y:2)
        assertTrue(serverClient.isConnected());

        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // When I send a move command with direction "NORTH"
        String moveRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"1\"]" +
                "}";
        response = serverClient.sendRequest(moveRequest);

        // Then the response should be "OK"
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // And the robot should be at position (x:2, y:3)
        assertNotNull(response.get("state"));
        JsonNode state = response.get("state");
        assertNotNull(state.get("position"));

        String disconnect = "{" +
                "  \"robot\": \"HAL\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }*/

    @Test
    void movementAtWorldBoundary() {
        // Given I have a robot at position (x:0, y:0)
        assertTrue(serverClient.isConnected());

        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        serverClient.sendRequest(launchRequest);

        // When I send a move command with direction "SOUTH"
        String moveRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"back\"," +
                "\"arguments\": [\"1\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(moveRequest);

        // Then I should get an "ERROR" response
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // And I should receive the message "Movement blocked by world boundary"
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
//        assertEquals("At the NORTH edge", response.get("data").get("message").asText());

        // And the robot should remain at position (x:0, y:0)
        assertNotNull(response.get("state"));
        JsonNode state = response.get("state");
        assertNotNull(state.get("position"));


        String disconnect = "{" +
                "  \"robot\": \"HAL\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }
}


