package za.co.wethinkcode.robots.acceptance_test2;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.commands.LaunchCommand;
import za.co.wethinkcode.robots.domain.handlers.VisibilityHandler;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.world.World;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class RobotsObstaclesTests {
    @Test
public void testLookDetectsObstacleAndRobotsIn2x2World() {
    // given a world of size 2x2 (coordinates from [-1,-1] to [1,1]).
    World world = new World(2, 2);

    //and the world has an obstacle at coordinate [0,1]
    // for a 2x2 world, valid coordinates are [-1,-1] to [1,1]
    // the obstacle bounds checking uses getMaxX() and getMaxY(), so for an obstacle at [x,y] with size [w,h]
    // it checks if [x+w, y+h] is within bounds. For [0,1] with size [1,1], it checks [1,2] which is out of bounds.
    // let's place the obstacle at [-1,0] with size [1,1], which checks [0,1] - this should be valid
    Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, -1, 0, 1, 1);
    assertTrue(world.addObstacle(obstacle), "Should be able to add obstacle at [-1,0]");

    //and I have successfully launched multiple robots into the world
    // since the current World implementation limits to 1 robot, we'll test with 1 robot
    // but manually add additional robots to the world's robot list for testing purposes.
    List<Robot> robots = new ArrayList<>();

    // launch the first robot through the normal process
    // the robot will be placed at [0,0] by default, which should be fine since obstacle is at [-1,0]
    Robot firstRobot = new Robot("Robot1", "tank");
    LaunchCommand launchCommand = new LaunchCommand(firstRobot, new String[]{"tank"});

    world.execute(launchCommand, "test-client", response -> {
        assertTrue(response.isOKResponse(), "Robot1 should launch successfully");
    });
    robots.add(firstRobot);

    //manually add additional robots to simulate having 8 robots in the world
    // We'll position them at different locations to test the look functionality
    // obstacle is at [-1,0], first robot is at [0,0]
    Robot robot2 = new Robot("Robot2", "tank");
    robot2.setPosition(0, 1); // North of first robot
    world.getRobots().add(robot2);
    robots.add(robot2);

    Robot robot3 = new Robot("Robot3", "tank");
    robot3.setPosition(1, 0); // East of first robot
    world.getRobots().add(robot3);
    robots.add(robot3);

    Robot robot4 = new Robot("Robot4", "tank");
    robot4.setPosition(0, -1); // South of first robot
    world.getRobots().add(robot4);
    robots.add(robot4);

    //add more robots at distance 1
    Robot robot5 = new Robot("Robot5", "tank");
    robot5.setPosition(1, 1); // Northeast
    world.getRobots().add(robot5);
    robots.add(robot5);

    Robot robot6 = new Robot("Robot6", "tank");
    robot6.setPosition(-1, 1); // Northwest (next to obstacle)
    world.getRobots().add(robot6);
    robots.add(robot6);

    Robot robot7 = new Robot("Robot7", "tank");
    robot7.setPosition(1, -1); // Southeast
    world.getRobots().add(robot7);
    robots.add(robot7);

    Robot robot8 = new Robot("Robot8", "tank");
    robot8.setPosition(-1, -1); // Southwest (next to obstacle)
    world.getRobots().add(robot8);
    robots.add(robot8);

    assertEquals(8, world.getRobots().size(), "Should have 8 robots in the world");

    //when I ask the first robot to look
    // we need to create a new VisibilityHandler with the updated robot list
    VisibilityHandler visibilityHandler = new VisibilityHandler(
            world.getRobots(),
            world.getObstacles(),
            world.getHalfWidth(),
            world.getHalfHeight(),
            world.getVisibility(),
            world
    );

    Response response = visibilityHandler.lookAround(firstRobot);

    //then I should get a response back
    assertTrue(response.isOKResponse(), "Look command should succeed");

    JSONObject data = response.object;
    assertNotNull(data, "Response should contain data");



    // the objects are in data.objects, not directly in objects
    JSONArray objects = data.getJSONObject("data").getJSONArray("objects");
    assertNotNull(objects, "Response should contain objects array");

    // count the objects found
    boolean foundObstacle = false;
    int robotCount = 0;

    for (int i = 0; i < objects.length(); i++) {
        JSONObject obj = objects.getJSONObject(i);
        String type = obj.getString("type");
        int distance = obj.getInt("distance");

        if ("OBSTACLE".equals(type)) {
            assertEquals(1, distance, "Obstacle should be at distance 1");
            foundObstacle = true;
        } else if ("ROBOT".equals(type)) {
            assertEquals(1, distance, "Robot should be at distance 1");
            robotCount++;
        }
    }

    //then I should get a response back with one object being an OBSTACLE that is one step away
    assertTrue(foundObstacle, "Should find an obstacle in the look response");

    // and three objects should be ROBOTs that is one step away
    // in a 2x2 world, from position [0,0] (first robot), the robot can see:
    // - North [0,1]: Robot2
    // - East [1,0]: Robot3
    // - South [0,-1]: Robot4
    // - West [-1,0]: OBSTACLE (blocked)
    //the robot should be able to see the obstacle and several other robots
    assertTrue(robotCount >= 3, "Should find at least 3 other robots in the look response, found: " + robotCount);
    }
    @Test
    public void testLookDetectsObstacleAndRobotsIn4x4World() {
        // given a world of size 4x4 (coordinates from [-2,-2] to [2,2])
        World world = new World(4, 4);
    }

    @Test
    public void testLookDetectsObstacleAndRobotsIn10x10World() {
        // given a world of size 10x10 (coordinates from [-5,-5] to [5,5])
        World world = new World(10, 10);
    }

        //and the world has an obstacle at coordinate [0,1]
}