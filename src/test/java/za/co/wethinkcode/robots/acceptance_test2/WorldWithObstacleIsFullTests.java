package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for scenario:
 * World with an obstacle is full

 * Assumes the reference server is running with:
 * java -jar reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1
 */
public class WorldWithObstacleIsFullTests {
    private static final int DEFAULT_PORT = 5000;
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    public void testWorldIsFullAfter8Robots() {
        assertTrue(serverClient.isConnected());

        // Launch 8 robots successfully
        for (int i = 0; i < 10;i++) {
            String launchRequest = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            if( response.get("result").equals("ERROR")) break;
            assertEquals("OK", response.get("result").asText());
        }

        //9th robot cant launch due to space limit
        String extraRobotRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRobotRequest);

        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                response.get("data").get("message").asText());

        // Disconnect all robots
        for (int i = 0; i < 8; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }
}