package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class Config2x2WithoutObstacleTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    public void canLaunchAnotherRobot() {
        assertTrue(serverClient.isConnected());

        String firstRobotRequest = "{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode firstResponse = serverClient.sendRequest(firstRobotRequest);
        assertEquals("OK", firstResponse.get("result").asText());

        String secondRobotRequest = "{" +
                "\"robot\": \"John\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode secondResponse = serverClient.sendRequest(secondRobotRequest);

        assertEquals("OK", secondResponse.get("result").asText());
        assertNotNull(secondResponse.get("state"));

        serverClient.sendRequest("{" +
                "\"robot\": \"Mbali\"," +
                "\"command\": \"disconnect\"," +
                "\"arguments\": []" +
                "}");
        serverClient.sendRequest("{" +
                "\"robot\": \"John\"," +
                "\"command\": \"disconnect\"," +
                "\"arguments\": []" +
                "}");

    }

    @Test
    void successfulMovementInClearPath() {
        // Given I have a robot at position (x:2, y:2)
        assertTrue(serverClient.isConnected());

        String launchRequest = "{" +
                "\"robot\": \"Robot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // When I send a move command with direction "NORTH"
        String moveRequest = "{" +
                "\"robot\": \"Robot\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"1\"]" +
                "}";
        response = serverClient.sendRequest(moveRequest);

        // Then the response should be "OK"
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // And the robot should be at position (x:2, y:3)
        assertNotNull(response.get("state"));
        JsonNode state = response.get("state");
        assertNotNull(state.get("position"));

        String disconnect = "{" +
                "  \"robot\": \"Robot\"," +
                "  \"command\": \"disconnect\"," +
                "  \"arguments\": []" +
                "}";
        serverClient.sendRequest(disconnect);
    }

    @Test
    public void testWorldIsFullAfter9Robots() {
        assertTrue(serverClient.isConnected());

        // Launch 8 robots successfully
        for (int i = 0; i < 9;i++) {
            String launchRequest = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            if( response.get("result").equals("ERROR")) break;
            assertEquals("OK", response.get("result").asText());
        }

        //9th robot cant launch due to space limit
        String extraRobotRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRobotRequest);

        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                response.get("data").get("message").asText());

        // Disconnect all robots
        for (int i = 0; i < 9; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }
}
