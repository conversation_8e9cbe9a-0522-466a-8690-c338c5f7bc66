package za.co.wethinkcode.robots.acceptance_test2;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldClient;
import za.co.wethinkcode.robots.acceptance_tests.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for scenario:
 * World without obstacles is full

 * Assumes the reference server is running with:
 * java -jar reference-server-0.2.3.jar -p 5000 -s 2
 */
public class WorldWithoutObstaclesIsFullTests {
    private static final int DEFAULT_PORT = 5000;
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() {
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    public void testWorldIsFullAfter9Robots() {
        assertTrue(serverClient.isConnected());

        // Launch 9 robots successfully
        for (int i = 0; i < 10; i++) {
            String request = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);
            if( response.get("result").equals("ERROR")) break;
            assertEquals("OK", response.get("result").asText());
        }


        String extraRobotRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(extraRobotRequest);

        assertEquals("ERROR", response.get("result").asText());
        assertEquals("No more space in this world",
                response.get("data").get("message").asText());

        // Disconnect all launched robots
        for (int i = 0; i < 9; i++) {
            serverClient.sendRequest("{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"disconnect\"," +
                    "\"arguments\": []" +
                    "}");
        }
    }
}