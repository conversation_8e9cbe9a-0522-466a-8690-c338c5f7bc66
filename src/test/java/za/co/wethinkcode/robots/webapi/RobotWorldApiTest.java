package za.co.wethinkcode.robots.webapi;

import org.junit.jupiter.api.*;

import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for the Robot World Web API
 */
public class RobotWorldApiTest {
    private static RobotWorldApiServer server;
    private static final int TEST_PORT = 8081;
    private static final String BASE_URL = "http://localhost:" + TEST_PORT;

    @BeforeAll
    public static void startServer() {
        server = new RobotWorldApiServer();
        server.start(TEST_PORT);
        
        // Give the server a moment to start
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @AfterAll
    public static void stopServer() {
        if (server != null) {
            server.stop();
        }
    }

    @Test
    @DisplayName("GET /world - Get current world state")
    public void testGetCurrentWorld() throws UnirestException {
        HttpResponse<String> response = Unirest.get(BASE_URL + "/world").asString();

        assertEquals(200, response.getStatus());

        String responseBody = response.getBody();
        assertNotNull(responseBody);
        assertTrue(responseBody.contains("width"));
        assertTrue(responseBody.contains("height"));
        assertTrue(responseBody.contains("obstacles"));
        assertTrue(responseBody.contains("robots"));
    }

    @Test
    @DisplayName("POST /robot/{name} - Launch a robot")
    public void testLaunchRobot() throws UnirestException {
        String launchCommand = "{\"command\":\"launch\",\"arguments\":[\"tank\"]}";

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/TestBot")
                .header("Content-Type", "application/json")
                .body(launchCommand)
                .asString();

        assertEquals(201, response.getStatus());

        String responseBody = response.getBody();
        assertNotNull(responseBody);
        assertTrue(responseBody.contains("result"));
    }

    @Test
    @DisplayName("POST /robot/{name} - Invalid command")
    public void testInvalidCommand() throws UnirestException {
        String invalidCommand = "{\"command\":\"invalid\",\"arguments\":[]}";

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/TestBot2")
                .header("Content-Type", "application/json")
                .body(invalidCommand)
                .asString();

        assertEquals(400, response.getStatus());
    }

    @Test
    @DisplayName("POST /robot/{name} - Missing fields")
    public void testMissingFields() throws UnirestException {
        String incompleteCommand = "{\"command\":\"launch\"}";

        HttpResponse<String> response = Unirest.post(BASE_URL + "/robot/TestBot3")
                .header("Content-Type", "application/json")
                .body(incompleteCommand)
                .asString();

        assertEquals(400, response.getStatus());
    }
}
