package za.co.wethinkcode.robots.webapi;

import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.commands.LaunchCommand;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.database.WorldDatabase;

import org.json.JSONObject;

import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the Web API handler functionality
 */
public class C {
    private World world;
    private WorldDatabase worldDatabase;

    @BeforeEach
    public void setUp() {
        world = World.getInstance();
        worldDatabase = new WorldDatabase();
    }

    @Test
    @DisplayName("Test createWorldJson method")
    public void testCreateWorldJson() {
        // Launch a robot to have some content in the world
        Robot robot = new Robot("TestBot", "tank");
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});
        
        AtomicReference<Response> responseRef = new AtomicReference<>();
        world.execute(launchCommand, "test-client", responseRef::set);
        
        Response response = responseRef.get();
        assertNotNull(response);
        assertTrue(response.isOKResponse());
        
        // Test that the world contains the robot
        assertEquals(1, world.getRobots().size());
        assertEquals("TestBot", world.getRobots().get(0).getName());
        assertEquals("tank", world.getRobots().get(0).getMake());
    }

    @Test
    @DisplayName("Test JSON command parsing")
    public void testJsonCommandParsing() {
        String jsonCommand = "{\"command\":\"launch\",\"arguments\":[\"sniper\"]}";
        
        try {
            JSONObject commandJson = new JSONObject(jsonCommand);
            
            assertTrue(commandJson.has("command"));
            assertTrue(commandJson.has("arguments"));
            
            String commandName = commandJson.getString("command");
            assertEquals("launch", commandName);
            
            var argumentsArray = commandJson.getJSONArray("arguments");
            assertEquals(1, argumentsArray.length());
            assertEquals("sniper", argumentsArray.getString(0));
            
        } catch (Exception e) {
            fail("JSON parsing should not fail: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Test robot launch via command execution")
    public void testRobotLaunchExecution() {
        String robotName = "ApiTestBot";
        String robotType = "tank";
        
        // Create robot and launch command
        Robot robot = new Robot(robotName, robotType);
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{robotType});
        
        // Execute command
        AtomicReference<Response> responseRef = new AtomicReference<>();
        world.execute(launchCommand, "web-api-client", responseRef::set);
        
        Response response = responseRef.get();
        assertNotNull(response);
        assertTrue(response.isOKResponse());
        
        // Verify robot was added to world
        Robot foundRobot = world.findRobot(robotName);
        assertNotNull(foundRobot);
        assertEquals(robotName, foundRobot.getName());
        assertEquals(robotType, foundRobot.getMake());
    }

    @Test
    @DisplayName("Test world state JSON structure")
    public void testWorldStateStructure() {
        // Add a robot to the world
        Robot robot = new Robot("StructureTestBot", "sniper");
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"sniper"});
        
        AtomicReference<Response> responseRef = new AtomicReference<>();
        world.execute(launchCommand, "test-client", responseRef::set);
        
        // Verify world state contains expected elements
        assertTrue(world.getWidth() > 0);
        assertTrue(world.getHeight() > 0);
        assertNotNull(world.getObstacles());
        assertNotNull(world.getRobots());
        assertTrue(world.getRobots().size() > 0);
        
        // Verify robot properties
        Robot foundRobot = world.getRobots().get(world.getRobots().size() - 1);
        assertEquals("StructureTestBot", foundRobot.getName());
        assertEquals("sniper", foundRobot.getMake());
        assertNotNull(foundRobot.getPosition());
        assertNotNull(foundRobot.getDirection());
    }
}
