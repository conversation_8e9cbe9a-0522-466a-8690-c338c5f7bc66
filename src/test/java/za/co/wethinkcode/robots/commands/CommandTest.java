package za.co.wethinkcode.robots.commands;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.commands.*;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.world.World;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

public class CommandTest {

    private World world;

    @BeforeEach
    public void setUp() {
        world = World.getInstance();
    }

    private World newWorld() {
        return new World(10, 10);
    }

    private Robot launchRobot(World world, String clientId, String name, String make) {
        Robot robot = new Robot(name, make);
        LaunchCommand cmd = new LaunchCommand(robot, new String[]{make});
        AtomicReference<Robot> launched = new AtomicReference<>();

        world.execute(cmd, clientId, response -> {
            System.out.println(response.toJSONString());
            assertTrue(response.isOKResponse());
            launched.set(world.getRobots().getFirst());
        });

        return launched.get();
    }

    @Test
    public void testCommands() {
        String[] valid = {"forward", "back", "turn", "look", "state", "launch"};
        String[] invalid = {"left", "go"};

        for (String cmd : valid) assertTrue(Command.isValidCommand(cmd));
        for (String cmd : invalid) assertFalse(Command.isValidCommand(cmd));
    }

    @Test
    public void testLaunchTwoRobotsPerClientLimit() {
        World world = newWorld();
        String clientId = "client-xyz";

        Robot robot = launchRobot(world, clientId, "Alpha", "tank");
        assertNotNull(robot);
    }

    @Test
    public void testLookAndOrientation() {
        World world = newWorld();
        String clientId = "client-abc";
        Robot robot = launchRobot(world, clientId, "Thato", "sniper");

        world.execute(new LookCommand(robot, new String[]{}), clientId, response -> {
            assertTrue(response.isOKResponse());
            System.out.println(response.object);
            assertNotEquals(0, response.object.getJSONObject("data").getJSONArray("objects").length());
        });

        world.execute(new OrientationCommand(robot), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals("Thato is facing NORTH.", response.object.getJSONObject("data").getString("message"));
        });
    }

    @Test
    public void testLookWithNoRobots() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot ghost = new Robot("Alpha", "tank");

        world.execute(new LookCommand(ghost, new String[]{}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals(4, response.object.getJSONObject("data").getJSONArray("objects").length());
        });
    }

    @Test
    public void testSuccessfulLaunchAndState() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        world.execute(new StateCommand(robot, new String[]{}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals("NORTH", response.object.getJSONObject("state").getString("direction"));
            assertEquals("NORMAL", response.object.getJSONObject("state").getString("status"));
        });
    }

    @Test
    public void testHandleTurn() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        world.execute(new TurnCommand(robot, new String[]{"left"}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals("Alpha turned left to WEST", response.object.getJSONObject("data").getString("message"));
            assertEquals("WEST", response.object.getJSONObject("state").getString("direction"));
        });

        world.execute(new TurnCommand(robot, new String[]{"right"}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals("Alpha turned right to NORTH", response.object.getJSONObject("data").getString("message"));
            assertEquals("NORTH", response.object.getJSONObject("state").getString("direction"));
        });
    }

    @Test
    public void testMoveAndPit() {
        World world = newWorld();
        world.setDimensions(10, 10);
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        // move forward
        robot.setPosition(0, 0);
        world.execute(new MoveCommand(robot, "forward", new String[]{"1"}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertTrue(response.object.getJSONObject("data").getString("message").contains("Moved Alpha to"));
        });

        // move back
        robot.setPosition(0, 0);
        world.execute(new MoveCommand(robot, "back", new String[]{"1"}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().contains("Moved Alpha to"));
        });

        // move into pit
        robot.setPosition(0, 0);
        world.addObstacle(new Obstacle(ObstacleType.PIT, 0, 1, 1, 1));
        world.displayWorld();
        world.execute(new MoveCommand(robot, "forward", new String[]{"1"}), clientId, response -> {
            System.out.println("pit test: " + response.object);
            assertFalse(response.isOKResponse());
            assertTrue(response.getMessage().contains("Alpha fell into a pit and died."));
            assertEquals(Robot.RobotStatus.Dead, robot.status);
        });
    }

    @Test
    public void testFireMissesAndHits() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot shooter = launchRobot(world, clientId, "Alpha", "tank");
        Robot target = launchRobot(world, clientId, "Hal", "tank");

        shooter.setPosition(0, 1);
        target.setPosition(0, 2);

        int shots = shooter.getShots();
        int shields = target.getShields();

        world.execute(new FireCommand(shooter, new String[]{}), clientId, response -> {
            assertEquals(shots - 1, response.object.getJSONObject("state").getInt("shots"));
            assertEquals(shields - 1, target.getShields());
            assertEquals(target.getName(), response.object.getJSONObject("data").getString("robot"));
        });
    }

    @Test
    public void testReloading() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        int shots = robot.getShots();
        world.execute(new FireCommand(robot, new String[]{}), clientId, response -> {
            assertEquals(shots - 1, robot.getShots());

            ReloadCommand reload = new ReloadCommand(robot, new String[]{});
            AtomicInteger turns = new AtomicInteger();

            world.execute(reload, clientId, r1 -> {
                turns.getAndIncrement();
                if (turns.get() == 1) {
                    assertEquals("Alpha is now reloading.", r1.object.getJSONObject("data").getString("message"));
                } else {
                    assertEquals("Alpha is done.", r1.object.getJSONObject("data").getString("message"));
                    assertEquals(shots, r1.object.getJSONObject("state").getInt("shots"));
                }
            });
        });
    }

    @Test
    public void testRepairing() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        int shields = robot.getShields();
        robot.takeHit();

        world.execute(new RepairCommand(robot, new String[]{}), clientId, response -> {
            assertTrue(response.isOKResponse());
            if (response.object.getJSONObject("data").getString("message").equalsIgnoreCase("Alpha is now repairing.")) {
                assertNotEquals(shields, robot.getShields());
            } else {
                assertEquals(shields, robot.getShields());
            }
        });
    }

    @Test
    public void testHelpCommand() {
        World world = newWorld();
        String clientId = "client-xyz";

        world.execute(new HelpCommand(null, null), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().contains("I CAN UNDERSTAND THESE COMMANDS"));
        });
    }

    @Test
    public void testShutdown() {
        World world = newWorld();
        String clientId = "client-xyz";
        Robot robot = launchRobot(world, clientId, "Alpha", "tank");

        world.execute(new ShutdownCommand(robot, new String[]{}), clientId, response -> {
            assertTrue(response.isOKResponse());
            assertEquals("Removed robot Alpha from the world.", response.object.getJSONObject("data").getString("message"));
        });
    }
}
