package za.co.wethinkcode.robots.commands;

import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;
import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.commands.MineCommand;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.handlers.CommandHandler;

import static org.junit.jupiter.api.Assertions.*;

class MineUnitTests {
    private World world;
    private Robot rover;
    private TestHandler handler;

    @BeforeEach
    void setUp() {
        world = World.getInstance();
        world.configure(5); // small world
        rover = new Robot("Rover1", "rover");
        rover.setPosition(0, 0);
        rover.setDirection(Direction.CardinalDirection.NORTH);
        rover.enableShields();
        world.addRobot(rover);
        handler = new TestHandler();
    }

    @AfterEach
    void tearDown() {
        // If World has no clear(), just remove the robot
        world.getRobots().clear();
        world.getObstacles().clear();
    }

    @Test
    void onlyRoversCanPlaceMines() {
        Robot bot = new Robot("Shooter1", "shooter");
        bot.setPosition(0, 0);
        world.addRobot(bot);

        MineCommand cmd = new MineCommand(bot, new String[0]);
        callHandleMine(cmd);
        assertEquals("ERROR", handler.response.object.getString("result"));
        assertTrue(handler.response.object.getJSONObject("data").getString("message").contains("Only rovers"));
    }

    @Test
    void cannotPlaceMineOnInvalidPosition() {
        rover.setDirection(Direction.CardinalDirection.NORTH);
        // Place obstacle in front (using MOUNTAIN as a blocking obstacle)
        world.addObstacle(new Obstacle(ObstacleType.MOUNTAIN, 0, -1, 0, 0));

        MineCommand cmd = new MineCommand(rover, new String[0]);
        callHandleMine(cmd);
        assertEquals("OK", handler.response.object.getString("result"));
        assertTrue(handler.response.object.getJSONObject("data").getString("message").contains("Cannot set mine"));
    }

    @Test
    void placeMineAndMoveForwardIfClear() {
        rover.setDirection(Direction.CardinalDirection.SOUTH);
        MineCommand cmd = new MineCommand(rover, new String[0]);
        callHandleMine(cmd);

        assertEquals("SETMINE", rover.status.name());
        assertTrue(world.getObstacles().stream().anyMatch(o -> o.type() == ObstacleType.MINE));
    }

    @Test
    void stepOnOwnMineReducesShields() {
        // Place mine and block forward movement
        rover.setDirection(Direction.CardinalDirection.NORTH);
        // Put obstacle 1 cell further to block after stepping on mine
        world.addObstacle(new Obstacle(ObstacleType.MOUNTAIN, 0, -2, 0, 0));

        MineCommand cmd = new MineCommand(rover, new String[0]);
        callHandleMine(cmd);

//        assertTrue(rover.getShields() < 7); // Default shields for rover
//        assertNotNull(handler.response.object.get("result"));
    }

    private void callHandleMine(MineCommand cmd) {
        try {
            Class<?> handlerClass = Class.forName("za.co.wethinkcode.robots.domain.handlers.CommandHandler");
            var method = handlerClass.getDeclaredMethod("handleMine", za.co.wethinkcode.robots.domain.commands.Command.class, CommandHandler.CompletionHandler.class);
            method.setAccessible(true);
            Object handlerInstance = handlerClass.getConstructor(World.class).newInstance(world);
            method.invoke(handlerInstance, cmd, handler);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static class TestHandler implements CommandHandler.CompletionHandler {
        Response response;
        @Override
        public void onComplete(Response response) {
            this.response = response;
        }
    }
}


