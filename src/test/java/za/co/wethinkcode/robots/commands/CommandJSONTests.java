package za.co.wethinkcode.robots.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Test;
import za.co.wethinkcode.robots.domain.commands.*;

import static org.junit.jupiter.api.Assertions.*;

public class CommandJSONTests {
    @Test
    public void testMovementCommands() {
        String forward = "forward";
        String back = "back";
        String name = "hal";
        String step = "1";

        // test "back" command with implicit robot name
        verifyMoveCommand(back + " " + step, name, back, step);
        
        // test "back" command with explicit robot name
        verifyMoveCommand(back + " " + step, "", back, step);
        
        // Test "forward" command with implicit robot name
        verifyMoveCommand(forward + " " + step, name, forward, step);
        
        // test "forward" command with explicit robot name.
        verifyMoveCommand(forward + " " + step, "", forward, step);
    }

    private void verifyMoveCommand(String input, String robotName, String expectedDirection, String expectedSteps) {
        String command = Command.fromInput(input, robotName);
        JSONObject cmd = new JSONObject(command);

        // verify command type and basic properties
//        assertEquals(MoveCommand.class, command.getClass());
        assertEquals(expectedDirection, cmd.getString("command"));
        
        // Verify arguments array.
        String[] expectedArgs = {expectedSteps};
        assertEquals(expectedArgs.length, cmd.getJSONArray("arguments").length());
//        assertArrayEquals(expectedArgs, cmd.getJSONArray("arguments"));
    }

    @Test
    public void testTurnCommand() {
        // test "turn right" with implicit robot name
        verifyTurnCommand("turn right", "hal", "right");
        
        // Test "turn right" with explicit robot name
        verifyTurnCommand("turn right", "", "right");
        
        // test "turn left" with implicit robot name
        verifyTurnCommand("turn left", "hal", "left");
        
        // test "turn left" with explicit robot name
        verifyTurnCommand("turn left", "", "left");
    }

    private void verifyTurnCommand(String input, String robotName, String expectedDirection) {
        String command = Command.fromInput(input, robotName);
        JSONObject cmd = new JSONObject(command);
        
        // Verify command type and basic properties
//        assertEquals(TurnCommand.class, command.getClass());
        assertEquals("turn", cmd.getString("command"));
        assertEquals(robotName, cmd.getString("robot"));
        
        // verify arguments array
        assertEquals(1, cmd.getJSONArray("arguments").length());
        assertEquals(expectedDirection, cmd.getJSONArray("arguments").get(0));
    }

    @Test
    public void testStateCommand() {
        verifySimpleCommand("state", StateCommand.class);
    }

    @Test
    public void testLookCommand() {
        verifySimpleCommand("look", LookCommand.class);
    }

    @Test
    public void testOrientationCommand() {
        verifySimpleCommand("orientation", OrientationCommand.class);
    }

    @Test
    public void testFireCommand() {
        verifySimpleCommand("fire", FireCommand.class);
    }

    @Test
    public void testReloadCommand() {
        verifySimpleCommand("reload", ReloadCommand.class);
    }

    @Test
    public void testRepairCommand() {
        verifySimpleCommand("repair", RepairCommand.class);
    }

    @Test
    public void testOffCommand() {
        verifySimpleCommand("off", ShutdownCommand.class);
    }

    @Test
    public void testDumpCommand() {
        String input = "dump";
        String command = Command.fromInput(input,"");
        JSONObject cmd = new JSONObject(command);

//        assertEquals(DumpCommand.class, command.getClass());
        assertEquals("dump", cmd.getString("command"));
    }

    @Test
    public void testLaunchCommand() {
        //Test launch with implicit robotname
        verifyLaunchCommand("launch tank hal", "hal", "tank");

        // test launch with explicit robot name
        verifyLaunchCommand("launch tank hal", "", "tank");
    }

    private void verifyLaunchCommand(String input, String robotName, String expectedRobotType) {
        String command = Command.fromInput(input, robotName);
        JSONObject cmd = new JSONObject(command);
        String name = cmd.getString("robot");
        String commandName2 = cmd.getString("command");
        JSONArray args = cmd.getJSONArray("arguments");

        // verify command type and basic properties
//        assertEquals(LaunchCommand.class, command.getClass());
        assertEquals("launch", commandName2);
        assertEquals(robotName, name);

        //Verify arguments
        String[] expectedArgs = {expectedRobotType};
        assertEquals(expectedArgs.length, args.length());
//        assertArrayEquals(expectedArgs, args);
    }

    /**
     * Helper method to verify simple commands that have no arguments
     * Tests both implicit and explicit robot name formats
     */
    private void verifySimpleCommand(String commandName, Class<?> expectedClass) {
        // test with implicit robot name
        String input = commandName;
        String robotName = "hal";
        String command = Command.fromInput(input, robotName);
        JSONObject cmd = new JSONObject(command);
        String name = cmd.getString("robot");
        String commandName2 = cmd.getString("command");
        JSONArray args = cmd.getJSONArray("arguments");
        
//        assertEquals(expectedClass, command.getClass());
        assertEquals(commandName, commandName2);
        assertEquals(robotName, name);
        assertEquals(0, args.length());
        
        // test with explicit robot name
        input = commandName;
        robotName = "";
        command = Command.fromInput(input, robotName);
        JSONObject commandJson = new JSONObject(command);
//        assertEquals(expectedClass, command.getClass());
        assertEquals(commandName, commandName2);
        assertEquals(robotName, commandJson.getString("robot"));
        assertEquals(0, args.length());
    }
}
