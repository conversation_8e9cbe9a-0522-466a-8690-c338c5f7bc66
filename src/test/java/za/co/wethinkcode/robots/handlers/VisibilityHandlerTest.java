package za.co.wethinkcode.robots.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;
import za.co.wethinkcode.robots.domain.handlers.VisibilityHandler;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.world.World;

import static org.junit.jupiter.api.Assertions.*;

public class VisibilityHandlerTest {

    private World world;
    private Robot robot;

    @BeforeEach
    public void setUp() {
        world = new World(10, 10);
        // You might need to modify World to allow more robots for testing
        robot = new Robot("Robot", "tank", 0, 0);
        world.addRobot(robot);
        robot.setPosition(0,0);
    }

    @Test
    public void testVisibility() {
        VisibilityHandler visibilityHandler = new VisibilityHandler(
                world.getRobots(),
                world.getObstacles(),
                world.getHalfWidth(),
                world.getHalfHeight(),
                world.getVisibility(),
                world
        );
        Response response = visibilityHandler.lookAround(robot);
        
        // Access objects through the data object
        assertNotNull(response.object.getJSONObject("data"));
        assertNotNull(response.object.getJSONObject("data").get("objects"));
        assertTrue(response.object.getJSONObject("data").getJSONArray("objects").length() >= 0);
    }

    @Test
    public void testVisibilityWithObstacle() {
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 1, 0, 1, 1);
        world.addObstacle(obstacle); // Add obstacle to the world
        VisibilityHandler visibilityHandler = new VisibilityHandler(
                world.getRobots(),
                world.getObstacles(),
                world.getHalfWidth(),
                world.getHalfHeight(),
                world.getVisibility(),
                world
        );
        robot.setPosition(3, 0);
        world.displayWorld();
        Response response = visibilityHandler.lookAround(robot);

        JSONObject jsonObject = (JSONObject)response.object.getJSONObject("data").getJSONArray("objects").get(3);
        assertEquals("OBSTACLE", jsonObject.getString("type"));
        assertEquals(Direction.CardinalDirection.WEST, jsonObject.get("direction"));
        assertEquals(2, jsonObject.getInt("distance"));
        assertFalse(response.object.getJSONObject("data").getJSONArray("objects").isEmpty());
    }

    @Test
    public void testVisibilityWithRobot() {
        Robot otherRobot = new Robot("OtherRobot", "tank", 1, 0);
        
        // Since World has maxRobots=1, manually add to the list for testing
        world.getRobots().add(otherRobot);
        otherRobot.setPosition(1, 0);
        
        System.out.println("World robots count: " + world.getRobots().size());
        
        VisibilityHandler visibilityHandler = new VisibilityHandler(
                world.getRobots(),
                world.getObstacles(),
                world.getHalfWidth(),
                world.getHalfHeight(),
                world.getVisibility(),
                world
        );
        Response response = visibilityHandler.lookAround(robot);
        
        // Access through data object
        JSONArray objects = response.object.getJSONObject("data").getJSONArray("objects");
        
        // Debug: Print all found objects
        System.out.println("Found " + objects.length() + " objects:");
        for (int i = 0; i < objects.length(); i++) {
            JSONObject obj = objects.getJSONObject(i);
            System.out.println("Object " + i + ": " + obj.toString());
        }
        
        assertFalse(objects.isEmpty());
        
        // Find the robot object instead of assuming index
        JSONObject robotObject = null;
        for (int i = 0; i < objects.length(); i++) {
            JSONObject obj = objects.getJSONObject(i);
            if ("ROBOT".equals(obj.getString("type"))) {
                robotObject = obj;
                break;
            }
        }
        
        assertNotNull(robotObject, "Should find a robot object");
        assertEquals("ROBOT", robotObject.getString("type"));
        assertEquals(Direction.CardinalDirection.EAST, robotObject.get("direction"));
        assertEquals(1, robotObject.getInt("distance"));
    }
}
