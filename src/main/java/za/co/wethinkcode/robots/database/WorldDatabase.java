package za.co.wethinkcode.robots.database;

import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.repository.WorldRepository;
import za.co.wethinkcode.robots.domain.world.World;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;

public class WorldDatabase implements WorldRepository {


    private final String DB_URL;

    public WorldDatabase() {
        this("libs/robot_worlds.db");
    }

    public WorldDatabase(String path) {
        this.DB_URL = "jdbc:sqlite:" + path;
        initDb();
    }

    private void initDb() {
        try {
            // ✅ Ensure the 'libs' directory exists before accessing DB
            Path dbPath = Paths.get("libs");
            if (!Files.exists(dbPath)) {
                Files.createDirectories(dbPath);
            }

            try (Connection conn = DriverManager.getConnection(DB_URL)) {
                Statement stmt = conn.createStatement();

                stmt.executeUpdate("""
                CREATE TABLE IF NOT EXISTS Worlds (
                    name TEXT PRIMARY KEY,
                    width INTEGER,
                    height INTEGER
                )
            """);

                stmt.executeUpdate("""
                CREATE TABLE IF NOT EXISTS Obstacles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    world_name TEXT,
                    type TEXT,
                    x INTEGER,
                    y INTEGER,
                    width INTEGER,
                    height INTEGER,
                    FOREIGN KEY(world_name) REFERENCES Worlds(name)
                )
            """);
            }

        } catch (SQLException | IOException e) {
            System.err.println("Failed to initialize database: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public boolean worldExists(String name) {
        String sql = "SELECT 1 FROM Worlds WHERE name = ?";
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, name);
            ResultSet rs = stmt.executeQuery();
            return rs.next();
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public void saveWorld(String name, World world) {
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            conn.setAutoCommit(false);

            // Remove old data if overwriting
            PreparedStatement deleteObstacles = conn.prepareStatement("DELETE FROM Obstacles WHERE world_name = ?");
            deleteObstacles.setString(1, name);
            deleteObstacles.executeUpdate();

            PreparedStatement deleteWorld = conn.prepareStatement("DELETE FROM Worlds WHERE name = ?");
            deleteWorld.setString(1, name);
            deleteWorld.executeUpdate();

            PreparedStatement insertWorld = conn.prepareStatement("INSERT INTO Worlds(name, width, height) VALUES (?, ?, ?)");
            insertWorld.setString(1, name);
            insertWorld.setInt(2, world.getWidth());
            insertWorld.setInt(3, world.getHeight());
            insertWorld.executeUpdate();

            PreparedStatement insertObstacle = conn.prepareStatement(
                    "INSERT INTO Obstacles(world_name, type, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?)"
            );

            for (Obstacle obstacle : world.getObstacles()) {
                insertObstacle.setString(1, name);
                insertObstacle.setString(2, obstacle.type().name());
                insertObstacle.setInt(3, obstacle.x());
                insertObstacle.setInt(4, obstacle.y());
                insertObstacle.setInt(5, obstacle.width());
                insertObstacle.setInt(6, obstacle.height());
                insertObstacle.addBatch();
            }

            insertObstacle.executeBatch();
            conn.commit();

            System.out.println("World '" + name + "' saved to database.");
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public void restoreWorld(String name, World world) {
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            PreparedStatement selectWorld = conn.prepareStatement("SELECT * FROM Worlds WHERE name = ?");
            selectWorld.setString(1, name);
            ResultSet rs = selectWorld.executeQuery();

            if (!rs.next()) {
                System.out.println("No world found with name: " + name);
                return;
            }

            int width = rs.getInt("width");
            int height = rs.getInt("height");
            world.configure(width);  // Reconfigure world

            PreparedStatement selectObstacles = conn.prepareStatement("SELECT * FROM Obstacles WHERE world_name = ?");
            selectObstacles.setString(1, name);
            ResultSet obsRS = selectObstacles.executeQuery();

            while (obsRS.next()) {
                ObstacleType type = ObstacleType.valueOf(obsRS.getString("type"));
                int x = obsRS.getInt("x");
                int y = obsRS.getInt("y");
                int w = obsRS.getInt("width");
                int h = obsRS.getInt("height");
                world.addObstacle(new Obstacle(type, x, y, w, h));
            }

            System.out.println("World '" + name + "' restored.");
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}