package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;
/**
 * Command that shuts down the robot.
 * When executed, it instructs the robot to power off.
 */
public class ShutdownCommand extends Command{
    public ShutdownCommand (Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "off";
    }
}