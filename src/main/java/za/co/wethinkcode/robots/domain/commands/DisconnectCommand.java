package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;
/**
 * Command to disconnect a robot from the server.
 * Used to trigger a graceful robot disconnection.
 */
public class DisconnectCommand extends Command {
    /**
     * Constructs a DisconnectCommand for the specified robot and arguments.
     * @param robot the robot to disconnect
     * @param arguments command arguments (if any)
     */
    public DisconnectCommand(Robot robot, String[] arguments) {
            super(robot, arguments);
    }
    /**
     * Returns the name of this command.
     * @return "disconnect"
     */
    @Override
    public String commandName() {
        return "disconnect";
    }
}