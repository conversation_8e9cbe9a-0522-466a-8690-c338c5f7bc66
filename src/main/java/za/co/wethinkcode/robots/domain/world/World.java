package za.co.wethinkcode.robots.domain.world;

import org.json.JSONArray;
import org.json.JSONObject;
import java.util.*;
import java.util.Random;

import za.co.wethinkcode.robots.domain.sharedclasses.Position;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.commands.Command;
import za.co.wethinkcode.robots.domain.handlers.CommandHandler;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.infrastructure.ConfigLoader;


public class World {
    private static final World INSTANCE = new World();
    private final CommandHandler commandHandler;
    private int width;
    private int height;
    private int halfWidth;
    private int halfHeight;
    private int maxShieldStrength;
    private int shieldRepairTime = 3000;
    private int reloadTime = 3000;
    private int visibility;
    private int mineTime = 3000;
    private final int maxRobots = 10;
    private final List<Obstacle> obstacles = new ArrayList<>();
    private final List<Robot> robots = new ArrayList<>();
    private List<Position> mines = new ArrayList<>();

    public World() {
        setDefaultDimensions();              // sets width = 100, height = 50, halfWidth = 50, etc.
        setDefaultWorldProperties();        // default shields, reloads, etc.
        this.commandHandler = new CommandHandler(this);
//        generateDefaultObstacles();
//        displayWorld();                     // optional: console display
    }

    public World(int size) {
        this.width = size;
        this.height = size;
        this.halfWidth = size / 2;
        this.halfHeight = size / 2;
        this.commandHandler = new CommandHandler(this);
    }

    public static World getInstance() {
        return INSTANCE;
    }




    private void initializeFromConfig() {
        ConfigLoader configLoader = new ConfigLoader();
        configLoader.applyConfigToWorld(this, "config.properties");
    }

    public World(int width, int height) {
        setDimensions(width, height);
        this.visibility = this.halfWidth;
        this.commandHandler = new CommandHandler(this);
    }

    public void setDimensions(int width, int height) {
        this.width = width;
        this.height = height;
        this.halfWidth = Math.max(1, width / 2);
        this.halfHeight = Math.max(1, height / 2);
    }

    public void setDefaultDimensions() {
        this.width = 100;
        this.height = 50;
        this.halfWidth = Math.max(1, width / 2);
        this.halfHeight = Math.max(1, height / 2);
    }

    public void setWorldProperties(int shieldRepairTime, int reloadTime, int maxShieldStrength, int visibility) {
        this.shieldRepairTime = shieldRepairTime;
        this.reloadTime = reloadTime;
        this.maxShieldStrength = maxShieldStrength; // Fixed bug: was using shieldRepairTime
        this.visibility = visibility;
    }

    public void setDefaultWorldProperties() {
        int visibility = (int) (this.getWidth() * 0.30);

        this.shieldRepairTime = 5;
        this.reloadTime = 3;
        this.maxShieldStrength = 10;
        this.visibility = visibility;
    }

    public void execute(Command command, String clientId, CommandHandler.CompletionHandler completionHandler) {
        commandHandler.handle(command, clientId, completionHandler);
    }

    public void displayWorld() {
        ViewportData viewport = new ViewportData(-halfWidth, halfHeight, width, height);
        System.out.println(displayViewport(viewport));
    }


    public String displayViewport(ViewportData viewport) {
        String[][] grid = initializeGrid(viewport);
        placeObstaclesOnGrid(grid, viewport);
        placeRobotsOnGrid(grid, viewport);
        return renderGridAsString(grid, viewport.height, viewport.width);
    }

    private String[][] initializeGrid(ViewportData viewport) {
        String[][] grid = new String[viewport.height][viewport.width];

        for (int i = 0; i < viewport.height; i++) {
            for (int j = 0; j < viewport.width; j++) {
                int worldX = viewport.originX + j;
                int worldY = viewport.originY - i;
                grid[i][j] = isWithinBounds(worldX, worldY) ? "◾️" : "  ";
            }
        }

        return grid;
    }

    private void placeObstaclesOnGrid(String[][] grid, ViewportData viewport) {
        for (Obstacle obstacle : obstacles) {
            placeObstacleOnGrid(grid, obstacle, viewport);
        }
    }

    /**
     * places a single obstacle on the grid if it's visible in the viewport.
     */
    private void placeObstacleOnGrid(String[][] grid, Obstacle obstacle, ViewportData viewport) {
        // Get the bounds of the obstacle
        int minX = obstacle.getX();
        int maxX = obstacle.getMaxX();
        int minY = obstacle.getY();
        int maxY = obstacle.getMaxY();

        // For each point in the obstacle
        for (int y = minY; y < maxY; y++) {
            for (int x = minX; x < maxX; x++) {
                // Skip if point is not in viewport
                if (!isPointInViewport(x, y, viewport)) {
                    continue;
                }

                // Convert world coordinates to grid coordinates
                int gridX = x - viewport.originX;
                int gridY = viewport.originY - y;

                // Skip if grid coordinates are invalid
                if (!isValidGridCoordinate(gridY, gridX, viewport.height, viewport.width)) {
                    continue;
                }

                // Place obstacle symbol on the grid
                grid[gridY][gridX] = obstacle.type().getSymbol();
            }
        }
    }

    private void placeRobotsOnGrid(String[][] grid, ViewportData viewport) {
        for (Robot robot : robots) {
            int x = robot.getX();
            int y = robot.getY();
            if (isPointInViewport(x, y, viewport)) {
                int gx = x - viewport.originX;
                int gy = viewport.originY - y;
                if (isValidGridCoordinate(gy, gx, viewport.height, viewport.width)) {
                    grid[gy][gx] = "🤖";
                }
            }
        }
    }
    private boolean isOccupied(Position position) {
        for (Robot robot : robots) {
            if (robot.getPosition().equals(position)) {
                return true;
            }
        }
        return false;
    }


    private boolean isPointInViewport(int worldX, int worldY, ViewportData viewport) {
        // Check if point is within viewport bounds (note: Y-axis is inverted, positive is up)
        boolean isXInBounds = worldX >= viewport.originX && worldX < viewport.originX + viewport.width;
        boolean isYInBounds = worldY <= viewport.originY && worldY > viewport.originY - viewport.height;
        return isXInBounds && isYInBounds;
    }

    /**
     * checks if the given row and column are valid coordinates within the grid.
     */
    private boolean isValidGridCoordinate(int row, int col, int height, int width) {
        return row >= 0 && row < height && col >= 0 && col < width;
    }

    private String renderGridAsString(String[][] grid, int height, int width) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                sb.append(grid[i][j]).append(" ");
            }
            sb.append("\n");
        }
        return sb.toString();
    }

    public String displayDirectionalCross(Robot robot, int maxDistance) {
        // Calculate view boundaries
        ViewBoundary boundary = calculateViewBoundary(robot, maxDistance);

        // Initialize grid
        String[][] grid = initializeCrossGrid(boundary);

        // Draw cross pattern and place objects
        populateCrossGrid(grid, robot, boundary);

        // Render grid to string
        return renderGridAsString(grid, boundary.height, boundary.width);
    }

    private void populateCrossGrid(String[][] grid, Robot robot, ViewBoundary boundary) {
        // Draw cross pattern
        drawCrossPattern(grid, robot, boundary);

        // Place obstacles
        placeObstaclesOnCross(grid, robot, boundary);

        // Place other robots
        placeOtherRobotsOnCross(grid, robot, boundary);

        // Place current robot
        placeCurrentRobot(grid, robot, boundary);
    }

    private ViewBoundary calculateViewBoundary(Robot robot, int maxDistance) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        int minX = Math.max(robotX - maxDistance, -halfWidth);
        int maxX = Math.min(robotX + maxDistance, halfWidth);
        int minY = Math.max(robotY - maxDistance, -halfHeight);
        int maxY = Math.min(robotY + maxDistance, halfHeight);

        int width = maxX - minX + 1;
        int height = maxY - minY + 1;

        return new ViewBoundary(minX, maxX, minY, maxY, width, height);
    }

    private String[][] initializeCrossGrid(ViewBoundary boundary) {
        String[][] grid = new String[boundary.height][boundary.width];
        for (int i = 0; i < boundary.height; i++) {
            for (int j = 0; j < boundary.width; j++) {
                grid[i][j] = "  ";
            }
        }
        return grid;
    }

    /**
     * draws a cross-pattern on the grid centered on the robot's position.
     * the cross consists of a horizontal and vertical line intersecting at the robot's position
     *
     * @param grid the grid to draw on
     * @param robot the robot at the center of the cross
     * @param boundary The view boundary.
     */
    private void drawCrossPattern(String[][] grid, Robot robot, ViewBoundary boundary) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        // Draw vertical line if robot is within horizontal bounds
        drawVerticalLine(grid, robotX, boundary);

        // Draw horizontal line if robot is within vertical bounds
        drawHorizontalLine(grid, robotY, boundary);
    }

    /**
     * Draws a vertical line on the grid at the robot's X coordinate.
     *
     * @param grid The grid to draw on
     * @param robotX The robot's X coordinate
     * @param boundary The view boundary
     */
    private void drawVerticalLine(String[][] grid, int robotX, ViewBoundary boundary) {
        // Only draw if the robot's X coordinate is within the boundary
        if (robotX >= boundary.minX && robotX <= boundary.maxX) {
            int col = robotX - boundary.minX;

            // Ensure column is valid
            if (col >= 0 && col < boundary.width) {
                for (int row = 0; row < boundary.height; row++) {
                    grid[row][col] = "◾️";
                }
            }
        }
    }

    /**
     * Draws a horizontal line on the grid at the robot's Y coordinate.
     *
     * @param grid The grid to draw on
     * @param robotY The robot's Y coordinate
     * @param boundary The view boundary
     */
    private void drawHorizontalLine(String[][] grid, int robotY, ViewBoundary boundary) {
        // Only draw if the robot's Y coordinate is within the boundary
        if (robotY >= boundary.minY && robotY <= boundary.maxY) {
            int row = boundary.maxY - robotY;

            // Ensure row is valid
            if (row >= 0 && row < boundary.height) {
                for (int col = 0; col < boundary.width; col++) {
                    grid[row][col] = "◾️";
                }
            }
        }
    }

    public void configure(int worldSize, Position obstaclePosition) {
        this.setDimensions(worldSize, worldSize);
        this.addObstacle(new Obstacle(ObstacleType.MOUNTAIN, obstaclePosition.getX(), obstaclePosition.getY(), 0, 0));
    }

    public void configure(int worldSize) {
        this.setDimensions(worldSize, worldSize);
    }


    /**
     * context class to hold cross-pattern drawing information.
     */
    private static class CrossContext {
        final String[][] grid;
        final Robot robot;
        final ViewBoundary boundary;
        final int robotX;
        final int robotY;

        CrossContext(String[][] grid, Robot robot, ViewBoundary boundary) {
            this.grid = grid;
            this.robot = robot;
            this.boundary = boundary;
            this.robotX = robot.getX();
            this.robotY = robot.getY();
        }
    }

    /**
     * places obstacles on the cross-shaped view centered on the robot.
     * only obstacles that intersect with the cross-pattern are displayed
     *
     * @param grid The grid to place obstacles on
     * @param robot The robot at the center of the view
     * @param boundary The view boundary
     */
    private void placeObstaclesOnCross(String[][] grid, Robot robot, ViewBoundary boundary) {
        CrossContext context = new CrossContext(grid, robot, boundary);

        for (Obstacle obstacle : obstacles) {
            placeObstacleIfOnCross(obstacle, context);
        }
    }

    /**
     * places an obstacle on the grid if it intersects with the cross-pattern.
     *
     * @param obstacle The obstacle to place
     * @param context The cross-drawing context
     */
    private void placeObstacleIfOnCross(Obstacle obstacle, CrossContext context) {
        // For each point in the obstacle
        for (int y = obstacle.getY(); y < obstacle.getMaxY(); y++) {
            for (int x = obstacle.getX(); x < obstacle.getMaxX(); x++) {
                // Skip if point is outside the view boundary
                if (!isPointInBoundary(x, y, context.boundary)) {
                    continue;
                }

                // Only place points that are on the same row or column as the robot (cross pattern)
                if (isOnSameRowOrColumn(x, y, context.robotX, context.robotY)) {
                    placeObstaclePointOnGrid(x, y, obstacle.type().getSymbol(), context);
                }
            }
        }
    }

    /**
     * Checks if a point is within the view boundary.
     *
     * @param x The x coordinate
     * @param y The y coordinate
     * @param boundary The view boundary
     * @return true if the point is within the boundary, false otherwise
     */
    private boolean isPointInBoundary(int x, int y, ViewBoundary boundary) {
        return x >= boundary.minX && x <= boundary.maxX &&
               y >= boundary.minY && y <= boundary.maxY;
    }

    /**
     * Places an obstacle point on the grid at the specified world coordinates.
     *
     * @param worldX The obstacle point's world x coordinate
     * @param worldY The obstacle point's world y coordinate
     * @param symbol The symbol to place on the grid
     * @param context The cross-drawing context
     */
    private void placeObstaclePointOnGrid(int worldX, int worldY, String symbol, CrossContext context) {
        int row = context.boundary.maxY - worldY;
        int col = worldX - context.boundary.minX;
        context.grid[row][col] = symbol;
    }

    /**
     * Places other robots on the cross-shaped view centered on the current robot.
     * Only robots that are in the same row or column as the current robot are displayed.
     *
     * @param grid The grid to place robots on
     * @param currentRobot The robot at the center of the view
     * @param boundary The view boundary
     */
    private void placeOtherRobotsOnCross(String[][] grid, Robot currentRobot, ViewBoundary boundary) {
        int robotX = currentRobot.getX();
        int robotY = currentRobot.getY();

        for (Robot other : robots) {
            // Skip the current robot
            if (other.equals(currentRobot)) {
                continue;
            }

            int otherX = other.getX();
            int otherY = other.getY();

            // Skip robots outside the view boundary
            if (!isRobotInBoundary(otherX, otherY, boundary)) {
                continue;
            }

            // Only place robots that are on the same row or column as the current robot
            if (isOnSameRowOrColumn(otherX, otherY, robotX, robotY)) {
                placeRobotOnGrid(grid, otherX, otherY, boundary);
            }
        }
    }

    /**
     * Checks if a robot is within the view boundary.
     *
     * @param x The robot's x coordinate
     * @param y The robot's y coordinate
     * @param boundary The view boundary
     * @return true if the robot is within the boundary, false otherwise
     */
    private boolean isRobotInBoundary(int x, int y, ViewBoundary boundary) {
        return x >= boundary.minX && x <= boundary.maxX &&
               y >= boundary.minY && y <= boundary.maxY;
    }

    /**
     * Checks if a position is on the same row or column as a reference position.
     *
     * @param x The x coordinate to check
     * @param y The y coordinate to check
     * @param refX The reference x coordinate
     * @param refY The reference y coordinate
     * @return true if on the same row or column, false otherwise
     */
    private boolean isOnSameRowOrColumn(int x, int y, int refX, int refY) {
        return x == refX || y == refY;
    }

    /**
     * Places a robot symbol on the grid at the specified world coordinates.
     *
     * @param grid The grid to place the robot on
     * @param worldX The robot's world x coordinate
     * @param worldY The robot's world y coordinates
     * @param boundary The view boundary for coordinate conversion
     */
    private void placeRobotOnGrid(String[][] grid, int worldX, int worldY, ViewBoundary boundary) {
        int row = boundary.maxY - worldY;
        int col = worldX - boundary.minX;
        grid[row][col] = "🤖";
    }

    private void placeCurrentRobot(String[][] grid, Robot robot, ViewBoundary boundary) {
        int robotX = robot.getX();
        int robotY = robot.getY();

        boolean isRobotInBoundaryX = robotX >= boundary.minX && robotX <= boundary.maxX;
        boolean isRobotInBoundaryY = robotY >= boundary.minY && robotY <= boundary.maxY;

        if (isRobotInBoundaryX && isRobotInBoundaryY) {
            int row = boundary.maxY - robotY;
            int col = robotX - boundary.minX;
            grid[row][col] = "🤖";
        }
    }

    // Helper class to store view boundary information
    private static class ViewBoundary {
        final int minX, maxX, minY, maxY, width, height;

        ViewBoundary(int minX, int maxX, int minY, int maxY, int width, int height) {
            this.minX = minX;
            this.maxX = maxX;
            this.minY = minY;
            this.maxY = maxY;
            this.width = width;
            this.height = height;
        }
    }

    public Status isPositionValid(Position position, Robot robot) {
        if (!isWithinBounds(position.getX(), position.getY())) {
            return Status.OutOfBounds;
        }

        Status obstacleStatus = checkObstacleCollisions(position);
        if (obstacleStatus != Status.OK) {
            return obstacleStatus;
        }

        for (Robot r : robots) {
            if (r.getX() == position.getX() && r.getY() == position.getY() && !r.getName().equals(robot.getName())) {
                return Status.PositionOccupied;
            }
        }

        return Status.OK;
    }


    private Status checkObstacleCollisions(Position position) {
        Status status = Status.OK;
        if (obstacles.size() == 0) {
            return status; // No obstacles to check
        }
        for (Obstacle obstacle : obstacles) {
            int obstacleX = obstacle.getX();
            int obstacleY = obstacle.getY();
            if (obstacleX == position.getX() && obstacleY == position.getY()) {
                if (obstacle.type() == ObstacleType.PIT) {
                    return Status.HitObstaclePIT;
                } else if (obstacle.type() == ObstacleType.MINE) {
                    return Status.HitMine;
                }
                else {
                    return Status.HitObstacle;
                }
            }
        }
        return status;
    }

    public boolean addObstacle(Obstacle obstacle) {
        if (isObstacleValid(obstacle)) {
            obstacles.add(obstacle);
            return true;
        }
        return false;
    }

    public void removeObstacle(Obstacle obstacle) {
        if (obstacles.contains(obstacle)) {
            obstacles.remove(obstacle);
        }
    }

    private boolean isObstacleValid(Obstacle obstacle) {
        boolean isWithinWorldBounds = isWithinBounds(obstacle.getMaxX(), obstacle.getMaxY());
        boolean doesNotOverlap = !overlapsWithExistingObstacles(obstacle);
        return doesNotOverlap && isWithinWorldBounds;
    }

    private boolean overlapsWithExistingObstacles(Obstacle newObstacle) {
        return obstacles.stream().anyMatch(existing -> existing.overlaps(newObstacle));
    }
    public Status addRobot(Robot robot) {
        Position position = new Position(robot.getX(), robot.getY());
        if (isPositionValid(position, robot) != Status.OK) {
            position = findFreePosition(robot);
        }
        if (position == null) {
            System.out.println("world full");
            return Status.WORLDFULL; // No space found
        } else {
            robot.setPosition(position.getX(), position.getY());
            for (Robot nextRobot : robots) {
                if (nextRobot.getName().equals(robot.getName())) {
                    System.out.println("name exists");
                    return Status.ExistingName;
                } else if (robot.getX() == nextRobot.getX() && robot.getY() == nextRobot.getY()) {
                    System.out.println("position overlaps");
                    return Status.PositionOccupied;
                }
            }
        }
        robot.setPosition(position.getX(), position.getY());
        robots.add(robot);
        return Status.OK;
    }

    private Position findFreePosition(Robot robot) {
        for (int x = -halfWidth; x <= halfWidth; x++) {
            for (int y = -halfHeight; y <= halfHeight; y++) {
                Position pos = new Position(x, y);
                Status positionStatus = isPositionValid(pos, robot);
                if (positionStatus == Status.OK && !isOccupied(pos)) {
                    return pos;
                }
            }
        }
        return null;
    }

    public Robot findRobot(String name) {
        for (Robot robot : robots) {
            if (robot.getName().equals(name)) {
                return robot;
            }
        }
        return null;
    }

    public Response removeRobot(String robotName) {
        Robot robot = findRobot(robotName);
        if (robot == null) {
            return createErrorResponse("Robot not found.");
        }

        robots.remove(robot);
        return createSuccessResponse("Removed robot " + robotName + " from the world.");
    }

    private Response createErrorResponse(String message) {
        return new Response("ERROR", message);
    }

    private Response createSuccessResponse(String message) {
        return new Response("OK", message);
    }

    public Response stateForRobot(Robot robot, Response response) {
        JSONObject state = createRobotStateJson(robot);
        response.object.put("state", state);
        return response;
    }

    private JSONObject createRobotStateJson(Robot robot) {
        JSONObject state = new JSONObject();

        JSONArray position = new JSONArray();
        position.put(robot.getX());
        position.put(robot.getY());

        state.put("position", position);
        state.put("direction", robot.orientation().toUpperCase());
        state.put("shields", robot.getShields());
        state.put("shots", robot.getShots());
        state.put("status", getRobotStatusString(robot));

        return state;
    }

    private String getRobotStatusString(Robot robot) {
        if (robot.isDead()) {
            return "DEAD";
        } else if (robot.isRepairing()) {
            return "REPAIR";
        } else if (robot.isReloading()) {
            return "RELOAD";
        }
        return "NORMAL";
    }

    public String getAllRobotsInfo() {
        if (robots.isEmpty()) {
            return "No robots in the world.";
        }

        StringBuilder sb = new StringBuilder("Robots in the world:");
        for (Robot robot : robots) {
            sb.append("\n-> ").append(robot.getName());
            sb.append("\n   "+ robot.getState());
        }
        return sb.toString();
    }

    public int getMineTime() {
        return mineTime;
    }

    private String formatRobotInfo(Robot robot) {
        Response response = new Response("", "State for " + robot.getName());
        stateForRobot(robot, response);
        return robot.getName() + " " + response.toJSONString();
    }

    public String getFullWorldState() {
        StringBuilder sb = new StringBuilder("World State:\n");
        appendWorldDimensions(sb);
        appendObstaclesInfo(sb);
        sb.append("\n");
        appendRobotsInfo(sb);
        sb.append("\n");
        return sb.toString();
    }

    private void appendWorldDimensions(StringBuilder sb) {
        sb.append("Dimensions: \n")
          .append("Width: " + width + "\n")
          .append("Height: " + height)
          .append("\n\n");
    }

    private void appendObstaclesInfo(StringBuilder sb) {
        sb.append("Obstacles - ")
          .append(obstacles.size())
          .append(":\n");

        for (Obstacle obs: obstacles) {
            sb.append(" -> " + obs.type() + " :\n")
            .append("  Position: " + obs.getX() + ", " + obs.getY() + "\n")
            .append("  Size: " + obs.width()+ "x" + obs.height() + "\n");
        }
    }

    private void appendRobotsInfo(StringBuilder sb) {

        sb.append(getAllRobotsInfo());
    }

    public List<Robot> getRobots() {
        return robots;
    }

    public List<Obstacle> getObstacles() {
        return obstacles;
    }

    public int getHalfWidth() {
        return halfWidth;
    }

    public int getHalfHeight() {
        return halfHeight;
    }

    public int getMaxShieldStrength() {
        return maxShieldStrength;
    }

    public int getReloadTime() {
        return reloadTime;
    }

    public int getShieldRepairTime() {
        return shieldRepairTime;
    }

    public int getVisibility() {
        return visibility;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    private void generateDefaultObstacles() {
        int obstacleCount = calculateObstacleCount();
        Random random = new Random();

        for (int i = 0; i < obstacleCount; i++) {
            addRandomObstacle(random);
        }
    }

    private int calculateObstacleCount() {
        return (int) ((height + width) * 0.30);
    }

    private void addRandomObstacle(Random random) {
        boolean added = false;
        int attempts = 0;
        final int MAX_ATTEMPTS = 20;

        while (!added && attempts < MAX_ATTEMPTS) {
            Obstacle obstacle = createRandomObstacle(random);
            added = addObstacle(obstacle);
            attempts++;
        }
    }

    private Obstacle createRandomObstacle(Random random) {
        int randomWidth = random.nextInt(1, 4);
        int randomHeight = random.nextInt(1, 4);
        int randomX = random.nextInt(-halfWidth, halfWidth);
        int randomY = random.nextInt(-halfHeight, halfHeight);

        ObstacleType type = ObstacleType.values()[random.nextInt(ObstacleType.values().length)];
        return new Obstacle(type, randomX, randomY, randomWidth, randomHeight);
    }

    private boolean isWithinBounds(int x, int y) {
        return x >= -halfWidth && x <= halfWidth  && y >= -halfHeight && y <= halfHeight;
    }

    // Create a ViewportData class to encapsulate viewport parameters
    private static class ViewportData {
        final int originX;
        final int originY;
        final int width;
        final int height;

        ViewportData(int originX, int originY, int width, int height) {
            this.originX = originX;
            this.originY = originY;
            this.width = width;
            this.height = height;
        }
    }
}
