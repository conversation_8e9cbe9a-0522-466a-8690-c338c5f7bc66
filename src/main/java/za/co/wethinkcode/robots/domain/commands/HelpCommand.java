package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that provides help information to the user.
 * When executed, it displays available commands and their usage.
 */
public class HelpCommand extends Command {
    public HelpCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "help";
    }
}
