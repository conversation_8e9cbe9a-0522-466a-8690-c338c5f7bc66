package za.co.wethinkcode.robots.domain.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.world.World;

import java.util.*;

import static za.co.wethinkcode.robots.domain.handlers.VisibleObject.ObjectType;

public class VisibilityHandler {
    private final List<Robot> robots;
    private final List<Obstacle> obstacles;
    private final int halfWidth;
    private final int halfHeight;
    private final int maxDistance;
    private final World world;

    public VisibilityHandler(List<Robot> robots, List<Obstacle> obstacles, int halfWidth, int halfHeight, int maxDistance, World world) {
        this.robots = robots;
        this.obstacles = obstacles;
        this.halfWidth = halfWidth;
        this.halfHeight = halfHeight;
        this.maxDistance = maxDistance;
        this.world = world;
    }

    public Response lookAround(Robot robot) {
        List<VisibleObject> visibleObjects = new ArrayList<>();
        JSONArray objectArray = new JSONArray();

        // Check all four directions
        for (Direction.CardinalDirection direction : Direction.CardinalDirection.values()) {
            List<VisibleObject> directionObjects = checkVisibleObjects(robot, direction, maxDistance);
            visibleObjects.addAll(directionObjects);
        }

        // Sort all objects by distance and filter
        visibleObjects.sort(Comparator
                .comparing((VisibleObject v) -> v.getDirection().toString())
                .thenComparingInt(VisibleObject::getDistance));

        // Convert to JSON
        visibleObjects.forEach(vo -> objectArray.put(vo.toJSON()));
        for (VisibleObject obstacle : visibleObjects) {
            System.out.println(obstacle.getType());
        }
        JSONObject data = new JSONObject();
        data.put("visibility", world.getVisibility());
        data.put("position", "[" + robot.getX() + "," + robot.getY() + "]");
        data.put("objects", objectArray);

        return world.stateForRobot(robot, Response.getFullResponse(data));
    }

    private List<VisibleObject> checkVisibleObjects(Robot robot, Direction.CardinalDirection direction, int maxDistance) {
        List<VisibleObject> visibleObjects = new ArrayList<>();
        int dx = getDx(direction);
        int dy = getDy(direction);

        int startX = robot.getX();
        int startY = robot.getY();

        if (isAtEdge(startX, startY, direction)) {
            visibleObjects.add(new VisibleObject(direction, 0, direction, ObjectType.EDGE));
        }

        for (int step = 1; step <= maxDistance; step++) {

            int x = startX + dx * step;
            int y = startY + dy * step;

            // Stop if we hit the edge
            if (isAtEdge(x, y, direction)) {
                System.out.println("Found edge at (" + x + "," + y + ")");
                visibleObjects.add(new VisibleObject(direction, step, direction, ObjectType.EDGE));
                break;
            }

            // Check for obstacle
            Optional<Obstacle> obstacle = findObstacleAt(x, y);
            if (obstacle.isPresent()) {
                visibleObjects.add(new VisibleObject(obstacle.get(), step, direction, ObjectType.OBSTACLE));
                if (obstacle.get().type() == ObstacleType.MOUNTAIN) {
                    break;
                }
                continue;
            }

            // Check for robot
            Optional<Robot> otherRobot = findRobotAt(x, y, robot);
            if (otherRobot.isPresent()) {
                visibleObjects.add(new VisibleObject(otherRobot.get(), step, direction, ObjectType.ROBOT));
            }
        }

        return visibleObjects;
    }


    private List<VisibleObject> filterVisibleObjects(List<VisibleObject> objects) {
        List<VisibleObject> result = new ArrayList<>();
        Set<Direction.CardinalDirection> directionsCovered = new HashSet<>();

        for (VisibleObject obj : objects) {
            // Only include the closest object in each direction
            if (!directionsCovered.contains(obj.getDirection())) {
                result.add(obj);
                directionsCovered.add(obj.getDirection());

                // Stop looking further in this direction if we hit a blocking obstacle
                if (obj.getType() == ObjectType.OBSTACLE &&
                        ((Obstacle)obj.getTarget()).type() == ObstacleType.MOUNTAIN) {
                    directionsCovered.add(obj.getDirection());
                }
            }
        }
        return result;
    }

    private Optional<Obstacle> findObstacleAt(int x, int y) {
        return obstacles.stream()
                .filter(o -> o.getX() == x && o.getY() == y)
                .findFirst();
    }

    private Optional<Robot> findRobotAt(int x, int y, Robot currentRobot) {
        return robots.stream()
                .filter(r -> !r.equals(currentRobot))
                .filter(r -> r.getX() == x && r.getY() == y)
                .findFirst();
    }

    private int getDx(Direction.CardinalDirection dir) {
        return switch (dir) {
            case EAST -> 1;
            case WEST -> -1;
            default -> 0;
        };
    }

    private int getDy(Direction.CardinalDirection dir) {
        return switch (dir) {
            case NORTH -> 1;
            case SOUTH -> -1;
            default -> 0;
        };
    }

    private boolean isAtEdge(int x, int y, Direction.CardinalDirection dir) {
//        System.out.println(halfHeight);
        return switch (dir) {
            case NORTH -> y == halfHeight;
            case SOUTH -> y == -halfHeight;
            case EAST -> x == halfWidth;
            case WEST -> x == -halfWidth;
        };
    }

    private static class CheckPosition {
        private final int x;
        private final int y;
        private final int step;
        private final Direction.CardinalDirection direction;

        public CheckPosition(int x, int y, int step, Direction.CardinalDirection direction) {
            this.x = x;
            this.y = y;
            this.step = step;
            this.direction = direction;
        }

        public int getX() { return x; }
        public int getY() { return y; }
        public int getStep() { return step; }
        public Direction.CardinalDirection getDirection() { return direction; }
    }
}