package za.co.wethinkcode.robots.domain.handlers;

import za.co.wethinkcode.robots.domain.robot.Robot;

public class FireResult {
    Robot hitRobot;
    int distance;

    FireResult(Robot hitRobot, int distance) {
        this.hitRobot = hitRobot;
        this.distance = distance;
    }

    static FireResult miss() {
        return new FireResult(null, 0);
    }

    boolean isHit() {
        return hitRobot != null;
    }
}

