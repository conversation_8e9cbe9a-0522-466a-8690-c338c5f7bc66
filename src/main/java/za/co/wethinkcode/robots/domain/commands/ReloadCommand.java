package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that reloads the robot's weapon or energy.
 * When executed, it instructs the robot to perform a reload action.
 */
public class ReloadCommand extends Command {
    public ReloadCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "reload";
    }
}
