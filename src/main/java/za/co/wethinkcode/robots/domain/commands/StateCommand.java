package za.co.wethinkcode.robots.domain.commands;
import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that retrieves the robot's current state.
 * When executed, it returns the robot's status information.
 */
public class StateCommand extends Command {
    public StateCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "state";
    }
}
