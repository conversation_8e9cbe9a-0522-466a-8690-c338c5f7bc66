package za.co.wethinkcode.robots.domain.sharedclasses;

/**
 * Represents a cardinal direction (NORTH, SOUTH, EAST, WEST) with the ability to
 * turn left or right, and provides a symbol representation for each direction.
 *  Direction class encapsulates the current direction of an object and
 *  * provides methods to rotate the direction either left (counterclockwise) or right (clockwise).

 */
public class Direction {
    public enum CardinalDirection {
        NORTH, SOUTH, EAST, WEST;

        public String symbolForDirection() {
            return switch (this) {
                case NORTH -> "⬆️";
                case SOUTH -> "⬇️";
                case EAST -> "➡️️";
                case WEST -> "⬅️";
            };
        }

        public Position getOffset() {
            return switch (this) {
                case NORTH -> new Position(0, 1);
                case SOUTH -> new Position(0, -1);
                case EAST  -> new Position(1, 0);
                case WEST  -> new Position(-1, 0);
            };
        }

    }

    private CardinalDirection direction;

    public Direction(CardinalDirection direction) {
        this.direction = direction;
    }

    public CardinalDirection getDirection() {
        return direction;
    }

    public CardinalDirection turnLeft() {
       return switch (direction) {
            case NORTH -> direction = CardinalDirection.WEST;
            case WEST -> direction = CardinalDirection.SOUTH;
            case SOUTH -> direction = CardinalDirection.EAST;
            case EAST -> direction = CardinalDirection.NORTH;
        };
    }

    public CardinalDirection turnRight() {
        return switch (direction) {
            case NORTH -> direction = CardinalDirection.EAST;
            case EAST -> direction = CardinalDirection.SOUTH;
            case SOUTH -> direction = CardinalDirection.WEST;
            case WEST -> direction = CardinalDirection.NORTH;
        };
    }

    @Override
    public String toString() {
        return direction.name();
    }
}