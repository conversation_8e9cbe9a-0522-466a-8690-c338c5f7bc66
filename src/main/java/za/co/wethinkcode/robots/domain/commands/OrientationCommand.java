package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that retrieves the robot's current orientation.
 * When executed, it returns the direction the robot is facing.
 */
public class OrientationCommand extends Command {
    public OrientationCommand(Robot robot) {
        super(robot, new String[]{});
    }

    @Override
    public String commandName() {
        return "orientation";
    }
}