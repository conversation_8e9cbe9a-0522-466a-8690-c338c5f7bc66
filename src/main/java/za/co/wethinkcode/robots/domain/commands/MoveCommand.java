package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that moves the robot in a specified direction.
 * When executed, it instructs the robot to move according to the given direction and arguments.
 */
public class MoveCommand extends Command {
    private final String direction;

    public MoveCommand(Robot robot, String direction, String[] arguments) {
        super(robot, arguments);
        this.direction = direction.toLowerCase();
    }

    @Override
    public String commandName() {
        return direction;
    }
}