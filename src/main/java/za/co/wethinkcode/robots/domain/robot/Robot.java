package za.co.wethinkcode.robots.domain.robot;

import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;
import za.co.wethinkcode.robots.domain.sharedclasses.Position;
import za.co.wethinkcode.robots.domain.world.Status;
import za.co.wethinkcode.robots.domain.world.World;

import static za.co.wethinkcode.robots.domain.sharedclasses.Direction.CardinalDirection.*;

/**
 * Models a robot with a name, position, direction, and state.
 * Handles robot-specific properties and actions.
 */
public class Robot {
    private String make;
    private String name;
    private int shields;
    private int savedShields;
    private int shots;
    private int maxShots;
    private int maxShields;
    private int shotDistance;
    private Direction direction;
    private Position position;
    public RobotStatus status;
    private boolean repairing;
    private boolean reloading;

//    public boolean areShieldsEnabled() {
//        return this.shieldsEnabled;   // or however you track it internally
//    }


    public enum RobotStatus {
        Normal,
        Dead,
        Reload,
        Repair,
        SETMINE
    }

    public Robot(String name, String make, int x, int y) {
        this.name = name;
        this.make = make;
        this.position =  new Position(x, y);
        this.direction= new Direction(NORTH); // default direction

        if (make.equalsIgnoreCase("tank")) {
            this.shields = 10;
            this.shots = 3;
            this.shotDistance = 3;
        } else if (make.equalsIgnoreCase("sniper")) {
            this.shields = 5;
            this.shots = 20;
            this.shotDistance = 5;
        }else {
            this.shields = 7;
            this.shots = 0;
            this.shotDistance = 0;
        }
        this.maxShields = shields;
        this.maxShots = this.shots;
        this.status = RobotStatus.Normal;
    }

    public Robot(String name) {
        this(name, "tank", 0, 0);
    }

    public Robot(String name, String make) {
        this(name, make, 0,0);
    }

    public void reduceShieldsByMine() {
        if (shields >= 3) {
            this.shields = this.shields - 3;
            this.status = RobotStatus.Normal;
        } else {
            this.status = RobotStatus.Dead;
        }
    }

    public void disableShields() {
        this.savedShields = this.shields;
        this.shields = 0;
        this.status = RobotStatus.SETMINE;
    }

    public void enableShields() {

            this.shields = this.savedShields;
    }

    public Status moveForward(World world) {
        int nextX = getPosition().getX();
        int nextY = getPosition().getY();

        switch (direction.getDirection()) {
            case NORTH -> nextY++;
            case SOUTH -> nextY--;
            case EAST  -> nextX++;
            case WEST  -> nextX--;
        }

        Status posStatus = world.isPositionValid(new Position(nextX, nextY), this);

        if (posStatus == Status.OK || posStatus == Status.HitMine) {
            setPosition(nextX, nextY);
        } else if (posStatus == Status.HitObstaclePIT) {
            this.status = RobotStatus.Dead;
        }

        return posStatus;
    }

    public Status moveBackward(World world) {
        int nextX = getPosition().getX();
        int nextY = getPosition().getY();

        switch (direction.getDirection()) {
            case NORTH -> nextY--;
            case SOUTH -> nextY++;
            case EAST  -> nextX--;
            case WEST  -> nextX++;
        }

        Status posStatus = world.isPositionValid(new Position(nextX, nextY), this);

        if (posStatus == Status.OK || posStatus == Status.HitMine) {
            setPosition(nextX, nextY);
        } else if (posStatus == Status.HitObstaclePIT) {
            this.status = RobotStatus.Dead;
        }

        return posStatus;
    }


    public Response getMoveResponse(Status positionStatus) {
        Response response = switch (positionStatus){
            case OK -> new Response("OK", "Done");
            case HitMine -> new Response("OK", "Mine");
            case HitObstaclePIT -> new Response("OK", "Fell");
            case HitObstacle, OutOfBounds -> new Response("OK", "Obstructed");
            default -> new Response("ERROR", "Obstructed");
        };
        return response;
    }

    public void setDirection(Direction.CardinalDirection direction) {
        this.direction = new Direction(direction);
    }

    public int getMaxShots() {
        return maxShots;
    }

    public int getMaxShields() {
        return maxShields;
    }

    public boolean isRepairing() {
        return this.repairing;
    }

    public void setRepairing(boolean repairing) {
        this.repairing = repairing;
    }

    public boolean isReloading() {
        return reloading;
    }

    public void setReloading(boolean reloading) {
        this.reloading = reloading;
    }

    public void setStatus(RobotStatus status) {
        this.status = status;
    }

    public Response turnLeft() {
        this.direction = new Direction(direction.turnLeft());
        return new Response("OK", "Done");
    }

    public Response turnRight() {
        this.direction = new Direction(direction.turnRight());
        return new Response("OK", "Done");
    }

    public String orientation() {
        return direction.toString();
    }

    public Position getPosition() {
        return position;
    }

    public int getX() {
        return position.getX();
    }

    public int getY() {
        return position.getY();
    }

    public String getName() {
        return name;
    }

    public String getMake() {
        return make;
    }

    public int getShields() {
        return shields;
    }

    public int getShots() {
        return shots;
    }

    public int getShotDistance() {
        return shotDistance;
    }

    public Direction getDirection() {
        return direction;
    }

    public void setShots(int shots) {
        this.shots = shots;
    }

    public void setPosition(int x, int y) {
        this.position = new Position(x, y);
    }

    public void setShields(int shields){
        this.shields = shields;
    }

    public void takeHit() {
        if (shields > 0) {
            this.shields--;
        } else {
            this.status = RobotStatus.Dead;
        }
    }
    public boolean isDead() {
        return this.status == RobotStatus.Dead;
    }

    public JSONObject getState() {
        JSONObject state = new JSONObject();
        state.put("position", position.getX() + "," + position.getY());
        state.put("direction", direction.getDirection().toString());
        state.put("shields", shields);
        state.put("shots", shots);
        state.put("status", status.toString());
        return state;
    }

}