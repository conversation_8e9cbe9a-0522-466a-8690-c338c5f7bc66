package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that turns the robot.
 * When executed, it instructs the robot to change its facing direction.
 */
public class TurnCommand extends Command {
    public TurnCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "turn";
    }
}