package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;
//import za.co.wethinkcode.robots.domain.Position;

/**
 * Command for placing a mine in the robot's current position.
 * Used to trigger mine placement action in the world.
 */
public class MineCommand extends Command {
    /**
     * Constructs a MineCommand for the specified robot and arguments.
     * @param robot the robot executing the mine command
     * @param args command arguments (if any)
     */
    public MineCommand(Robot robot, String[] args) {
        super(robot, args);
    }

    /**
     * Returns the name of this command.
     * @return "mine"
     */
    @Override
    public String commandName() {
        return "mine";
    }

}
