package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command to fire a weapon from the robot.
 * Used to trigger the robot's firing action in the world.
 */
public class FireCommand extends Command {
    /**
     * Constructs a FireCommand for the specified robot and arguments.
     * @param robot the robot executing the fire command
     * @param arguments command arguments (if any)
     */
    public FireCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    /**
     * Returns the name of this command.
     * @return "fire"
     */
    @Override
    public String commandName() {
        return "fire";
    }
}