package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command to dump the robot's internal state or diagnostics.
 * Used for debugging or inspection purposes.
 */
public class DumpCommand extends Command {
    /**
     * Constructs a DumpCommand for the specified robot and arguments.
     * @param robot the robot to dump state from
     * @param arguments command arguments (if any)
     */
    public DumpCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }
    /**
     * Returns the name of this command.
     * @return "dump"
     */
    @Override
    public String commandName() {
        return "dump";
    }

}