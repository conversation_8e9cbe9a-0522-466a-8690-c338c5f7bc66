package za.co.wethinkcode.robots.domain.responses;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Represents a standardized response object used for communication between the client and server.
 * Encapsulates a JSON structure with result, message, and optional data for consistency.
 */
public class Response {
    public final JSONObject object;

    public static String responseFromJSONString(String string) {
        JSONObject data;
        JSONObject state;
        StringBuilder response = new StringBuilder();
        try {
            JSONObject jsonObject = new JSONObject(string);
            data = jsonObject.getJSONObject("data");

            if (jsonObject.get("result").equals("ERROR")) {
                return data.getString("message");
            }

            state = jsonObject.getJSONObject("state");
        } catch (JSONException e) {
            return "Invalid JSON" + e;
        }

        response.append("Data: ").append(data).append("\n");
        response.append("State: ").append(state);

        return String.valueOf(response);
    }

    public Response(String result, String message) {
        this.object = new JSONObject();
        JSONObject data = new JSONObject();
        this.object.put("result", result);
        data.put("message", message);
        this.object.put("data", data);
    }

    public static Response ok(JSONObject data, String message) {
        Response response = new Response("OK", "");
        response.object.remove("data");
        response.object.put("data", data);

        return response;
    }

    public static Response getFullResponse(JSONObject data) {
        Response response = new Response("OK", "");
        response.object.remove("data");
        response.object.put("data", data);

        return response;
    }

    public String getMessage() {
//        JSONObject data = object.getJSONObject("data");
//        if (data.has("message")) {
//            JSONObject dataObject = new JSONObject(data);
//            return dataObject.getString("message");
//        }
//
//        String response = data + "\n" +
//                object.get("state");

        return String.valueOf(object);
    }

    public String toJSONString() {
        return String.valueOf(object);
    }

    public JSONObject getObject() {
        return this.object;
    }

    public boolean isOKResponse() {
        return this.object.getString("result").equalsIgnoreCase("OK");
    }
}
