package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that repairs the robot.
 * When executed, it instructs the robot to perform a repair action.
 */
public class RepairCommand extends Command {
    public RepairCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "repair";
    }
}
