package za.co.wethinkcode.robots.domain.repository;

import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;

import java.util.List;

/**
 * Repository interface for World persistence operations.
 * This interface belongs to the domain layer but will be implemented by the database layer.
 */
public interface WorldRepository {
    boolean worldExists(String name);
    void saveWorld(String name, World world);
    void restoreWorld(String name, World world);
}
