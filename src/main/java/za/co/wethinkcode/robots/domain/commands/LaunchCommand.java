package za.co.wethinkcode.robots.domain.commands;

import za.co.wethinkcode.robots.domain.robot.Robot;

/**
 * Command that launches a robot into the world.
 * When executed, it initializes the robot with the given arguments.
 */
public class LaunchCommand extends Command {
    public LaunchCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "launch";
    }
}
