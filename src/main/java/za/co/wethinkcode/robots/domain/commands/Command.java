package za.co.wethinkcode.robots.domain.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.robot.Robot;

import java.util.Arrays;

/**
 * Abstract representation of a command sent to robots.
 * Defines interface and common behavior for all commands.
 */
public abstract class Command {
    /**
     * The robot that executes this command.
     */
    public Robot robot;
    /** The make/type of the robot (optional, may be used by subclasses). */
    public String make;
    /** Arguments for the command. */
    public String[] arguments;
    /**
     * Constructs a Command for the given robot and arguments.
     * @param robot the robot to execute the command on
     * @param arguments the command arguments
     */
    public Command(Robot robot, String[] arguments) {
        this.robot = robot;
        this.arguments = arguments;
    }

    /**
     * Checks if the given string is a valid command.
     * @param command the command string to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidCommand(String command) {
        return switch (command.toLowerCase()) {
            case "forward", "back", "turn", "look", "state", "launch", "dump", "orientation", "shutdown",
                 "disconnect", "fire", "repair", "reload", "help", "mine" -> true;
            default -> false;
        };
    }
    /**
     * Serializes this command to a JSON string.
     * @return JSON string representation of the command
     */
    public String toJSONString() {
        JSONObject json = new JSONObject();
        json.put("command", commandName().toLowerCase());
        json.put("arguments", arguments);

        if (robot != null) {
            json.put("robot", robot.getName());
        }

        return json.toString();
    }
    /**
     * Creates a Command instance from a JSON object and robot.
     * @param json the JSON object containing command data
     * @param robot the robot to associate with the command
     * @return a Command instance
     * @throws IllegalArgumentException if the command is unknown or invalid
     */
    public static Command fromJSON(JSONObject json, Robot robot) {
        String command = json.getString("command").toLowerCase();

        String robotName = json.getString("robot");
        JSONArray jsonArgs = json.getJSONArray("arguments");
        String[] args = new String[jsonArgs.length()];

        for (int i = 0; i < jsonArgs.length(); i++) {
            args[i] = jsonArgs.getString(i);
        }

        return switch (command) {
            case "repair" -> new RepairCommand(robot, args);
            case "reload" -> new ReloadCommand(robot, args);
            case "help" -> new HelpCommand(robot, new String[]{});
            case "dump" -> new DumpCommand(robot, new String[]{});
            case "look" -> new LookCommand(robot, new String[]{});
            case "state" -> new StateCommand(robot, new String[]{});
            case "launch" -> new LaunchCommand(new Robot(robotName, args[0]), args);
            case "forward" -> new MoveCommand(robot, "forward", args);
            case "back" -> new MoveCommand(robot, "back", args);
            case "turn" -> new TurnCommand(robot, args);
            case "mine" -> new MineCommand(robot, args);
            case "orientation" -> new OrientationCommand(robot);
            case "off" -> new ShutdownCommand(robot, new String[]{});
            case "fire" -> new FireCommand(robot, args);
            case "disconnect" -> new DisconnectCommand(robot, new String[]{});
            default -> throw new IllegalArgumentException("Unknown command: " + command);
        };
    }

    /**
     * Parses user input into a JSON command string for sending to the server.
     *
     * @param input     the raw user input (e.g., "launch tank Robo1")
     * @param robotName the name of the robot sending the command
     * @return JSON string representing the command and arguments
     */
    public static String fromInput(String input, String robotName) {
        String[] tokens = input.trim().split(" ");
        if (tokens.length == 0 || tokens[0].isEmpty()) {
            throw new IllegalArgumentException("Invalid or empty command");
        }

        String command = tokens[0].toLowerCase();
        String[] args = new String[0]; // default empty arguments

        // Handle launch command separately
        if (command.equals("launch")) {
            if (tokens.length != 3) {
                throw new IllegalArgumentException("Invalid launch command. Format: launch <type> <name>");
            }

            String type = tokens[1].toLowerCase();
            boolean isValidRobotType = Arrays.asList("tank", "sniper", "rover").contains(type);
            if (!isValidRobotType) {
                throw new IllegalArgumentException("Invalid launch command. Format: launch <type> <name>");
            }

            args = new String[]{type};
        }
        else {
            // Lists of commands
            String[] simpleCommands = {"look", "state", "mine", "orientation", "fire", "repair", "reload", "off", "dump", "disconnect", "help"};
            String[] movementCommands = {"forward", "back", "turn"};
            String[] directions = {"left", "right"};

            if (Arrays.asList(simpleCommands).contains(command)) {
                if (tokens.length != 1) {
                    throw new IllegalArgumentException("Invalid launch command. Format: launch <type> <name>");
                }
            }
            else if (Arrays.asList(movementCommands).contains(command)) {
                if (tokens.length != 2) {
                    throw new IllegalArgumentException("Invalid command. To see available commands use: 'help'");
                }

                if (command.equals("turn")) {
                    String dir = tokens[1].toLowerCase();
                    if (!Arrays.asList(directions).contains(dir)) {
                        throw new IllegalArgumentException("Invalid turn direction. Use: left or right");
                    }
                    args = new String[]{dir};
                } else { // forward or back
                    try {
                        int distance = Integer.parseInt(tokens[1]);
                        args = new String[]{String.valueOf(distance)};
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("Invalid move distance. Must be a number.");
                    }
                }
            } else {
                throw new IllegalArgumentException("Unknown command: " + command);
            }
        }

        JSONObject json = new JSONObject()
                .put("robot", robotName)
                .put("command", command)
                .put("arguments", new JSONArray(Arrays.asList(args)));

        return json.toString();
    }


    /**
 * Returns the name of the command.
 * @return command name as a string
 */
    public abstract String commandName();
}
