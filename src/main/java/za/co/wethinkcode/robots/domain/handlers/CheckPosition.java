package za.co.wethinkcode.robots.domain.handlers;

import za.co.wethinkcode.robots.domain.sharedclasses.Direction;

public class CheckPosition {
        private final int x;
        private final int y;
        private final int step;
        private final Direction.CardinalDirection direction;

        public CheckPosition(int x, int y, int step, Direction.CardinalDirection direction) {
            this.x = x;
            this.y = y;
            this.step = step;
            this.direction = direction;
        }

        public int getX() { return x; }
        public int getY() { return y; }
        public int getStep() { return step; }
        public Direction.CardinalDirection getDirection() { return direction; }
}

