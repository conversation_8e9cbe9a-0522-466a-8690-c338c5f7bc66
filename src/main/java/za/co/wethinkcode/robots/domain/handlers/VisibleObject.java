package za.co.wethinkcode.robots.domain.handlers;

import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;

public class VisibleObject {
    public enum ObjectType { ROBOT, OBSTACLE, EDGE }

    private final Object target;
    private final int distance;
    private final Direction.CardinalDirection direction;
    private final ObjectType type;

    public VisibleObject(Object target, int distance, Direction.CardinalDirection direction, ObjectType type) {
        this.target = target;
        this.distance = distance;
        this.direction = direction;
        this.type = type;
    }

    public Object getTarget() {
        return target;
    }

    public int getDistance() {
        return distance;
    }

    public Direction.CardinalDirection getDirection() {
        return direction;
    }

    public ObjectType getType() {
        return type;
    }

    public JSONObject toJSON() {
        return new JSONObject()
                .put("type", type.toString())
                .put("direction", direction)
                .put("distance", distance);
    }
}
