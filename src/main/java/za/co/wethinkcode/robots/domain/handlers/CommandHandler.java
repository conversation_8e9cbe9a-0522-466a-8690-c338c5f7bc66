package za.co.wethinkcode.robots.domain.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.obstacles.Obstacle;
import za.co.wethinkcode.robots.domain.obstacles.ObstacleType;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.sharedclasses.Direction;
import za.co.wethinkcode.robots.domain.sharedclasses.Position;
import za.co.wethinkcode.robots.domain.commands.*;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.world.Status;
import za.co.wethinkcode.robots.domain.world.World;

import java.util.*;

public class CommandHandler {
    private Object command;

    @FunctionalInterface
    public interface CompletionHandler {
        void onComplete(Response response);
    }

    private final World world;
    private final Map<String, HashMap<String, String>> clientRobots = new HashMap<>();
    private VisibilityHandler visibilityHandler;

    public CommandHandler(World world) {
        this.world = world;

        this.visibilityHandler = new VisibilityHandler(
                world.getRobots(),
                world.getObstacles(),
                world.getHalfWidth(),
                world.getHalfHeight(),
                world.getVisibility(),
                world
        );
    }
    /**
     * Handles commands by directing each command to its specific handling logic.
     * This method uses a switch expression with pattern matching to determine the
     * specific type of Command and calls the corresponding handler method.*/

    public void handle(Command command, String clientId, CompletionHandler handler) {
        System.out.println("Executing command: " + command.commandName());

        switch (command) {
            case HelpCommand helpCommand -> handleHelp(helpCommand, handler);
            case LaunchCommand launchCommand -> handleLaunch(launchCommand, clientId, handler);
            case StateCommand stateCommand -> handleState(stateCommand, command.robot.getName(), handler);
            case OrientationCommand orientationCommand -> handleOrientation(orientationCommand, command.robot.getName(), handler);
            case LookCommand lookCommand -> handleLook(lookCommand, command.robot.getName(), handler);
            case MoveCommand moveCommand -> handleMove(moveCommand, handler);
            case TurnCommand turnCommand -> handleTurn(turnCommand, turnCommand.robot.getName(), handler);
            case ShutdownCommand shutdownCommand -> handleShutdown(shutdownCommand, handler);
            case DisconnectCommand ignored -> removeRobot(command.robot.getName(), handler);
            case FireCommand ignored -> handleFire(command.robot, handler);
            case ReloadCommand reloadCommand -> handleReload(reloadCommand, handler);
            case RepairCommand repairCommand -> handleRepair(repairCommand, handler);
            case MineCommand mineCommand -> handleMine(command, handler);
            default -> handler.onComplete(new Response("Error", "Unsupported command"));
        };
    }

    private void handleMine(Command command, CompletionHandler handler) {
        Robot robot = command.robot;
        if (robot == null) {
            handler.onComplete(new Response("ERROR", "Robot not found."));
            return;
        }
        if (!robot.getMake().equalsIgnoreCase("rover")) {
            handler.onComplete(new Response("ERROR", "Only rovers can set mines."));
            return;
        }
        Direction direction = robot.getDirection();
        Position position = switch (direction.getDirection()) {
            case NORTH -> new Position(robot.getX(), robot.getY() - 1);
            case SOUTH -> new Position(robot.getX(), robot.getY() + 1);
            case EAST -> new Position(robot.getX() - 1, robot.getY());
            case WEST -> new Position(robot.getX() + 1, robot.getY());
        };

        Status isPositionValid = world.isPositionValid(position, command.robot);
        if (isPositionValid != Status.OK) {
            robot.enableShields();
            robot.setStatus(Robot.RobotStatus.Normal);
            handler.onComplete(new Response("OK", "Cannot set mine here, " + isPositionValid));
            return;
        }
        Obstacle mine = new Obstacle(ObstacleType.MINE, position.getX(), position.getY(), 0, 0);
        Boolean added = world.addObstacle(mine);
        System.out.println("shields before disabling: " + robot.getShields());
        robot.disableShields();

        if (!added) {
            robot.enableShields();

            robot.setStatus(Robot.RobotStatus.Normal);
            handler.onComplete(new Response("OK", "Unable to set mine here. Please try again."));
            return;
        }

        // Try to move forward one step
        Status status = robot.moveForward( world);
        if (status != Status.OK) {
            robot.moveBackward(world);
            robot.enableShields();
            robot.reduceShieldsByMine();

            if (robot.isDead()) {
                robot.setStatus(Robot.RobotStatus.Dead);
                world.removeObstacle(mine);
                world.removeRobot(command.robot.getName());
                handler.onComplete(new Response("ERROR", "You are dead."));
                return;
            }

            world.removeObstacle(mine);
        } else {
            robot.enableShields();
        }
        robot.setStatus(Robot.RobotStatus.SETMINE);
        try {
            Thread.sleep(world.getMineTime());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Thread was interrupted while sleeping.");
        }
        JSONObject data = new JSONObject();
        data.put("message", "Done");
        data.put("visibility", world.getVisibility());
        data.put("position", new JSONArray().put(robot.getX()).put(robot.getY()));
        data.put("direction", robot.getDirection().getDirection());
//        data.put("shieldsEnabled", robot.areShieldsEnabled());

        Response response = Response.getFullResponse(data);
        response = world.stateForRobot(robot,response);
        handler.onComplete(response);

    }

    private void removeRobot(String robotName, CompletionHandler handler) {
        clientRobots.remove(robotName);
        Response response = world.removeRobot(robotName);
        handler.onComplete(response);
    }

    private void handleHelp(HelpCommand robot, CompletionHandler handler) {
        JSONObject helpJson = new JSONObject();

        helpJson.put("❓ help", "Show this help message 🆘");
        helpJson.put("🧭 orientation", "What direction you are facing");
        helpJson.put("forward <name> <n>", "Move forward by n steps (max 5) ⏩");
        helpJson.put("back <name> <n>", "Move backward by n steps (max 5) ⏪");
        helpJson.put("left", "Turn left 🔄  e.g. turn <name> left");
        helpJson.put("right", "Turn right 🔁 e.g. turn <name> right");
        helpJson.put("look", "List visible objects 👀");
        helpJson.put("state", "Show current robot status 📊");
        helpJson.put("fire", "Fire a shot (tank or sniper rules) 🔫");
        helpJson.put("reload", "Refill your ammo to maximum 🔄💥");
        helpJson.put("repair", "Restore your shields (takes time) 🛠️🛡️");
        helpJson.put("disconnect", "Disconnect the client completely 🫤");
        helpJson.put("launch", "Launch another robot 🚀 e.g. <type> <name>");



        Response response = new Response("OK", helpJson.toString());
        response = world.stateForRobot(robot.robot, response);
        handler.onComplete(response);
    }

    private void handleLaunch(LaunchCommand command, String clientId, CompletionHandler completionHandler) {

        String robotName = command.robot.getName();
        Response response;
        clientRobots.putIfAbsent(clientId, new HashMap<>());
        Status status = world.addRobot(command.robot);

        if (status == Status.OK) {
            response = new Response("OK", "Done");
            trackClientRobot(clientId, robotName, command.robot);
            JSONObject data = buildLaunchData(command.robot);
            response.object.put("data", data);
            world.stateForRobot(command.robot, response);
        } else {
            String error = handleLaunchResult(status);
            response =new Response("ERROR", error);
        }
        completionHandler.onComplete(response);
    }

    private void trackClientRobot(String clientId, String robotName, Robot robot) {
        clientRobots.get(clientId).put(robotName, robot.getMake());
    }

    private String handleLaunchResult(Status status) {
        return switch (status) {
            case PositionOccupied -> "Position occupied";
            case ERROR -> "ERROR";
            case OK -> "OK";
            case WORLDFULL ->"No more space in this world";
            case HitObstaclePIT -> "Fell into a pit and died.";
            case ExistingName -> "Too many of you in this world";
            case OutOfBounds -> "At world edge";
            case HitObstacle -> "Hit an obstacle";
            case HitMine -> "Hit a mine.";
        };
    }

    private JSONObject buildLaunchData(Robot robot) {
        JSONObject data = new JSONObject();
        data.put("position", new JSONArray().put(robot.getX()).put(robot.getY()));
        data.put("visibility", this.world.getVisibility());
        data.put("reload", this.world.getReloadTime());
        data.put("repair", this.world.getShieldRepairTime());
        data.put("shields", this.world.getMaxShieldStrength());
        return data;
    }


    private void handleMove(MoveCommand command, CompletionHandler handler) {
        Response errorResponse;

        errorResponse = validateMoveCommand(command);
        if (errorResponse != null) {
            handler.onComplete(errorResponse);
            return;
        }

        String direction = command.commandName();
        int steps = parseSteps(command.arguments[0]);

        Robot robot = command.robot;
        errorResponse = validateRobotState(robot);
        if (errorResponse != null) {
            handler.onComplete(errorResponse);
            return;
        }

        Status moved = moveRobotSteps(robot, direction, steps, handler);
        Response response = robot.getMoveResponse(moved);
        response = world.stateForRobot(robot, response);

        handler.onComplete(response);
    }

    private Response validateMoveCommand(MoveCommand command) {
        if (command.arguments.length != 1) {
            return new Response("ERROR", "Invalid move command format. Use '<direction> <robotName> <steps>'.");
        }
        String direction = command.commandName();
        if (!direction.equals("forward") && !direction.equals("back")) {
            return new Response("ERROR", "Invalid move direction. Use 'forward' or 'back'.");
        }
        return null;
    }

    private Status moveRobotSteps(Robot robot, String direction, int steps, CompletionHandler handler) {
        Status status = Status.OK;
        for (int i = 1; i < steps + 1; i++) {
            if (direction.equals("forward")) {
                status = robot.moveForward(world);

            } else {
                status = robot.moveBackward(world);
                System.out.println("steps status: "+status);
            }


            if (status == Status.HitMine) {
                Obstacle mine = new Obstacle(ObstacleType.MINE, robot.getX(), robot.getY(), 0, 0);
                world.removeObstacle(mine);
                robot.reduceShieldsByMine();

            }
        }
        return status;
    }


    private Response validateRobotState(Robot robot) {
        if (robot == null) return new Response("ERROR", "Robot not found.");
        if (robot.status == Robot.RobotStatus.Dead)
            return new Response("ERROR", robot.getName() + " is DEAD and cannot move.");
        if (robot.status == Robot.RobotStatus.Reload)
            return new Response("ERROR", robot.getName() + " is reloading and cannot move.");
        if (robot.status == Robot.RobotStatus.Repair)
            return new Response("ERROR", robot.getName() + " is repairing and cannot move.");
        return null;
    }

    private void handleState(StateCommand command, String robotName, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(robotName);
        if (robot != null) {

            Response response = new Response("OK", "Done");
            world.stateForRobot(robot, response);

            completionHandler.onComplete(response);
        } else {
            completionHandler.onComplete(new Response("ERROR", "Robot does not exist"));
        }
    }

    private void handleLook(LookCommand command, String robotName, CompletionHandler completionHandler) {
        if (robotName == null || robotName.isBlank()) {
            // Get the first robot in the world (if any) and use its name
            List<Robot> robots = world.getRobots();
            if (robots.isEmpty()) {
                completionHandler.onComplete(new Response("ERROR", "No robots available in the world."));
                return;
            }

            robotName = robots.getFirst().getName();
        }

        Robot robot = command.robot;

        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robotName));
            return;
        }
        visibilityHandler = new VisibilityHandler(world.getRobots(),world.getObstacles(),world.getHalfWidth(),world.getHalfHeight(), world.getVisibility(), world);
        completionHandler.onComplete(visibilityHandler.lookAround(robot));
    }

    private void handleOrientation(OrientationCommand command, String robotName, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(robotName);
        if (robot != null) {
            String direction = robot.orientation(); // Get the current direction
            completionHandler.onComplete(new Response("OK", direction));
        } else {
            completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robotName));
        }
    }

    private void handleTurn(TurnCommand turnCommand, String robotName, CompletionHandler completionHandler) {
        Response response = new Response("OK", "Done");
        Position position = turnCommand.robot.getPosition();

        String directionInput = turnCommand.arguments[0].toLowerCase();
        Robot robot = turnCommand.robot;

        if (robot.status == Robot.RobotStatus.Reload) {
            response = new Response("ERROR", robot.getName() + " is reloading and cannot turn");
        }

        if (robot.status == Robot.RobotStatus.Repair) {
            response = new Response("ERROR", robot.getName() + " is repairing and cannot turn");
        }

        if ("left".equalsIgnoreCase(directionInput)) {
            response = robot.turnLeft();
        } else if ("right".equalsIgnoreCase(directionInput)) {
            response = robot.turnRight();
        }
        robot.setPosition(position.getX(), position.getY());
        response = world.stateForRobot(robot, response);
        System.out.println("before completing: "+response.toJSONString());
        completionHandler.onComplete(response);
    }

    private void handleFire(Robot robot, CompletionHandler handler) {
        robot = world.findRobot(robot.getName());

        if (!isFireValid(robot, handler)) return;

        Direction.CardinalDirection direction = robot.getDirection().getDirection();
        Position offset = direction.getOffset();

        int range = robot.getShotDistance();

        FireResult result = findHitRobot(robot, offset, range);
        robot.setShots(robot.getShots() - 1);


        if (result.hitRobot == null) {
            Response response = new Response("OK", "Miss");
            response.object.put("data", new JSONObject().put("message", "Miss"));
            response = world.stateForRobot(robot, response);
            handler.onComplete(response);
            return;
        }

        result.hitRobot.takeHit();
        if (result.hitRobot.isDead()) {
            result.hitRobot.status = Robot.RobotStatus.Dead;
        }

        Response hitResponse = new Response("OK", "Hit");

        JSONObject data = new JSONObject();
        data.put("message", "Hit");
        data.put("distance", result.distance);
        data.put("robot", result.hitRobot.getName());

        hitResponse.object.put("data", data);
        hitResponse = world.stateForRobot(robot, hitResponse);

        handler.onComplete(hitResponse);

    }

    private boolean isFireValid(Robot robot, CompletionHandler handler) {
        if (robot == null) {
            handler.onComplete(new Response("ERROR", "Robot not found"));
            return false;
        }
        if (robot.getShots() <= 0) {
            handler.onComplete(new Response("ERROR", "You have no shots remaining."));
            return false;
        }
        if (robot.status == Robot.RobotStatus.Reload) {
            handler.onComplete(new Response("ERROR", robot.getName() + " is reloading."));
            return false;
        }
        if (robot.status == Robot.RobotStatus.Repair) {
            handler.onComplete(new Response("ERROR", robot.getName() + " is repairing."));
            return false;
        }
        return true;
    }

    private FireResult findHitRobot(Robot shooter, Position offset, int range) {
        Position current = shooter.getPosition();

        for (int step = 0; step <= range; step++) {
            Position check = new Position(
                    current.getX() + step * offset.getX(),
                    current.getY() + step * offset.getY()
            );
            Robot hitRobot = null;
            for (Robot robot: world.getRobots()) {
                if (robot.getX() == shooter.getX() && robot.getY() == shooter.getY()) {
                    System.out.println("Skipping shooter robot in hit check: " + robot.getName());
                    continue;
                }
                if (robot.getPosition().getX() == check.getX() && robot.getPosition().getY() == check.getY()) {
                    hitRobot = robot;
                }
            }

            if (hitRobot != null) {
                System.out.println("Hit robot: " + hitRobot.getName() + " at step: " + step);
                return new FireResult(hitRobot, step);
            }
        }

        return FireResult.miss();
    }


    private void handleShutdown(ShutdownCommand command, CompletionHandler completionHandler) {
        String robotName = command.robot.getName();
        clientRobots.remove(robotName);
        completionHandler.onComplete(world.removeRobot(robotName));
    }

    private void handleRepair(RepairCommand command, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(command.robot.getName());
        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Robot not found: " + command.robot.getName()));
            return;
        }

        // Check if the robot is already repairing
        if (robot.isRepairing()) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is already repairing."));
            return;
        }

        robot.setRepairing(true);
        try {
            robot.setShields(robot.getMaxShields());
            Response response = new Response("OK", "Done");

            Thread.sleep(world.getReloadTime() * 1000);
            robot.setRepairing(false);
            response = world.stateForRobot(robot, response);
            completionHandler.onComplete(response);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Thread was interrupted while sleeping.");
        }

    }

    private void handleReload(ReloadCommand command, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(command.robot.getName());

        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Robot not found: " + command.robot.getName()));
            return;
        }

        if (robot.isReloading()) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is already reloading."));
            return;
        }

        try {
            robot.setReloading(true);
            robot.setShots(robot.getMaxShots());
            Response response = new Response("OK", "Done");

            Thread.sleep(world.getReloadTime() * 1000);
            robot.setReloading(false);
            response = world.stateForRobot(robot, response);
            completionHandler.onComplete(response);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Thread was interrupted while sleeping.");
        }
    }

    private int parseSteps(String arg) {
        try {
            return Integer.parseInt(arg);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}