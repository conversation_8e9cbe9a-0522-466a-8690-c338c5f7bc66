package za.co.wethinkcode.robots.infrastructure;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.domain.repository.WorldRepository;
import za.co.wethinkcode.robots.domain.sharedclasses.Position;
import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.database.WorldDatabase;
import za.co.wethinkcode.robots.webapi.RobotWorldApiHandler;

import io.javalin.Javalin;

import java.io.IOException;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;

import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.ConcurrentHashMap;

import kong.unirest.HttpResponse;
import kong.unirest.Unirest;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
public class Server {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;
    private static Thread serverThread;
    private static int portNumber = 5000;
    private static int httpApiPort = 8080;
    private static int worldSize = 2;
    private static Position obstaclePosition;
    private static World world;
    private static Map<String, Socket> robotHandlers = new ConcurrentHashMap<>();
    private static Javalin javalinApp;
    /**
     * Entry point for the server application.
     * Initializes the world, starts the server socket, admin console, and HTTP API server.
     * @param args command-line arguments for server configuration
     */
    public static void main(String[] args) {
        try {
            parseArgs(args);
            setupWorld();
            startServerSocket();
            startRecorder();
            startHttpApi();
            startAdminConsole(world);

            serverThread = new Thread(() -> {
                try {
                    acceptClients();
                } catch (IOException e) {
                    if (isRunning) {
                        System.out.println("Server error in acceptClients: " + e.getMessage());
                    }
                }
            }, "ServerThread");
            serverThread.start();

            System.out.println("Server started on port " + portNumber);
            // no shutdown() here
        } catch (Exception e) {
            System.out.println("Server error: " + e.getMessage());
        }
    }

    public static void startServer(int port) throws IOException {
        parseArgs(new String[]{"-p", String.valueOf(port)});
        setupWorld();
        startServerSocket();
        startRecorder();
        startHttpApi();
        startAdminConsole(world);

        serverThread = new Thread(() -> {
            try {
                acceptClients();
            } catch (IOException e) {
                if (isRunning) {
                    System.out.println("Server error in acceptClients: " + e.getMessage());
                }
            }
        }, "ServerThread");
        serverThread.start();

        System.out.println("Test server started on port " + port);
    }


    private static void setupWorld() {
        world = World.getInstance();
        if (obstaclePosition == null) {
            world.configure(worldSize);
        } else {
            world.configure(worldSize, obstaclePosition);
        }
    }

    private static void startServerSocket() throws IOException {
        serverSocket = new ServerSocket(portNumber);
        System.out.println("Server started on port " + portNumber + ". Waiting for clients...");
    }

    private static void startRecorder() {
        try {
            new Recorder().logRun();
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    private static void startHttpApi() {
        try {
            javalinApp = Javalin.create();

            // Setup Web API routes
            setupWebApiRoutes();

            // Start the Javalin server
            javalinApp.start(httpApiPort);
            System.out.println("HTTP API started at port: " + httpApiPort);
        } catch (Exception e) {
            System.out.println("Failed to start HTTP API server: " + e.getMessage());
        }
    }

    private static void setupWebApiRoutes() {
        WorldDatabase worldDatabase = new WorldDatabase();

        // GET /world - Get current world state
        javalinApp.get("/world", context -> RobotWorldApiHandler.getCurrentWorld(context, world));

        // GET /world/{name} - Get world by name from database
        javalinApp.get("/world/{name}", context -> RobotWorldApiHandler.getWorldByName(context, world, worldDatabase));

        // POST /robot/{name} - Execute robot command
        javalinApp.post("/robot/{name}", context -> RobotWorldApiHandler.executeRobotCommand(context, world));
    }

    private static void acceptClients() throws IOException {
        while (isRunning) {
            try {
                Socket clientSocket = serverSocket.accept();
                System.out.println("\nNew client connected: " + clientSocket.getRemoteSocketAddress());
                new Thread(new ClientHandler(clientSocket, world)).start();
            } catch (IOException e) {
                if (!isRunning) {
                    System.out.println("Server shutdown.");
                    break;
                }
                throw e;
            }
        }
    }
    /**
     * Parses command-line arguments and configures the world and server settings.
     * @param args command-line arguments
     * @throws IllegalArgumentException if a flag is missing its value or is unknown
     */
    public static void parseArgs(String[] args) {
        for (int i = 0; i < args.length; i += 2) {
            if (i + 1 >= args.length) {
                throw new IllegalArgumentException("Missing value for flag: " + args[i]);
            }
            configureWorld(args[i], args[i + 1]);
        }
    }
    /**
     * Configures the world based on the provided flag and value.
     * @param flag configuration flag (e.g., -p, -s, -o, -h)
     * @param value value for the flag
     * @throws IllegalArgumentException for invalid flags or values
     */
    private static void configureWorld(String flag, String value) {
        switch (flag) {
            case "-p":
                portNumber = Integer.parseInt(value);
                break;
            case "-s":
                worldSize = Integer.parseInt(value);
                break;
            case "-h":
                httpApiPort = Integer.parseInt(value);
                break;
            case "-o":
                String[] coords = value.split(",");
                if (coords.length != 2) {
                    throw new IllegalArgumentException("Obstacle coordinates must be in 'x,y' format");
                }
                try {
                    int x = Integer.parseInt(coords[0].trim());
                    int y = Integer.parseInt(coords[1].trim());
                    obstaclePosition = new Position(x, y);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid obstacle coordinates: " + value, e);
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown flag: " + flag);
        }
    }
    /**
     * Registers a robot handler socket for the given robot name.
     * @param robotName name of the robot
     * @param socket associated client socket
     */
    public static void registerRobotHandler(String robotName, Socket socket) {
        robotHandlers.put(robotName, socket);
    }
    /**
     * Unregisters the robot handler for the given robot name.
     * @param robotName name of the robot to remove
     */
    public static void unregisterRobotHandler(String robotName) {
        robotHandlers.remove(robotName);
    }
    /**
     * Starts the admin console in a separate thread, allowing server control via commands.
     * @param world the current world instance
     */
    // src/main/java/za/co/wethinkcode/robots/infrastructure/Server.java
    // Java
    private static void startAdminConsole(World world) {
        new Thread(() -> {
            Scanner scanner = new Scanner(System.in);
            WorldRepository db = new WorldDatabase();

            while (isRunning) {
                System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'purge', 'display', 'save <name>', 'restore <name>', 'testapi'");
                System.out.print("[Admin]: ");
                String input = scanner.nextLine().trim();
                String[] parts = input.split("\\s+", 2);
                String command = parts[0].toLowerCase();

                switch (command) {
                    case "quit":
                        handleQuit();
                        break;
                    case "robots":
                        handleRobots(world);
                        break;
                    case "dump":
                        handleDump(world);
                        break;
                    case "purge":
                        handlePurge(parts, world);
                        break;
                    case "display":
                        handleDisplay(world);
                        break;
                    case "save":
                        handleSave(parts, db, world, scanner);
                        break;
                    case "restore":
                        handleRestore(parts, db, world);
                        break;
                    case "testapi":
                        handleTestApi();
                        break;
                    default:
                        System.out.println("Unknown admin command.");
                }
            }
        }, "AdminConsole").start();
    }

    private static void handleQuit() {
        System.out.println("Shutting down server...");
        shutdown();
    }

    private static void handleRobots(World world) {
        System.out.println(world.getAllRobotsInfo());
    }

    private static void handleDump(World world) {
        System.out.println(world.getFullWorldState());
    }

    private static void handlePurge(String[] parts, World world) {
        if (parts.length < 2) {
            System.out.println("Usage: purge <robot_name>");
            return;
        }
        String name = parts[1].toLowerCase();
        Socket socket = robotHandlers.get(name);
        if (socket == null) {
            System.out.println("Robot '" + name + "' not found.");
            return;
        }
        world.removeRobot(name);
        try (PrintWriter out = new PrintWriter(socket.getOutputStream(), true)) {
            ClientHandler.disconnect("You have been purged from the world.", socket, name, out);
        } catch (IOException e) {
            System.out.println("Error sending purge message to robot: " + e.getMessage());
        }
    }

    private static void handleDisplay(World world) {
        world.displayWorld();
    }

    private static void handleSave(String[] parts, WorldRepository db, World world, Scanner scanner) {
        if (parts.length < 2) {
            System.out.println("Usage: save <world_name>");
            return;
        }
        String saveName = parts[1];
        if (db.worldExists(saveName)) {
            System.out.print("World '" + saveName + "' exists. Overwrite? (y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            if (!confirm.equals("y")) {
                System.out.println("Save cancelled.");
                return;
            }
        }
        db.saveWorld(saveName, world);
        System.out.println("World saved as '" + saveName + "'");
    }

    private static void handleRestore(String[] parts, WorldRepository db, World world) {
        if (parts.length < 2) {
            System.out.println("Usage: restore <world_name>");
            return;
        }
        String restoreName = parts[1];
        if (!db.worldExists(restoreName)) {
            System.out.println("World '" + restoreName + "' does not exist.");
            return;
        }
        db.restoreWorld(restoreName, world);
        System.out.println("World '" + restoreName + "' restored.");
    }

    private static void handleTestApi() {
        System.out.println("Testing HTTP API endpoints...");
        String getUrl = "http://localhost:5000/api/world/restore";
        HttpResponse<String> getResponse = Unirest.get(getUrl).asString();
        System.out.println("GET Response Status Code: " + getResponse.getStatus());
        System.out.println("GET Response Body: " + getResponse.getBody());
        String postUrl = "http://localhost:5000/api/world/save";
        String jsonBody = "{\"world\":\"example_state\"}";
        HttpResponse<String> postResponse = Unirest.post(postUrl)
                .header("Content-Type", "application/json")
                .body(jsonBody)
                .asString();
        System.out.println("POST Response Status Code: " + postResponse.getStatus());
        System.out.println("POST Response Body: " + postResponse.getBody());
    }
    /**
     * Shuts down the server gracefully, closing sockets and interrupting threads.
     */
    public static void shutdown() {
        isRunning = false;
        try {
            // Stop the Javalin HTTP API server
            if (javalinApp != null) {
                javalinApp.stop();
                System.out.println("HTTP API server stopped");
            }

            // Stop the socket server
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close(); // this will cause accept() to throw
            }
            if (serverThread != null) {
                serverThread.join(); // wait for the thread to fully exit
            }
        } catch (IOException | InterruptedException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }
}