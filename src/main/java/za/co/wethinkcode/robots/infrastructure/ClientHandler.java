package za.co.wethinkcode.robots.infrastructure;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.lang.Runnable;

import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONException;
import za.co.wethinkcode.robots.domain.commands.Command;
import za.co.wethinkcode.robots.domain.handlers.CommandHandler;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.world.World;

/**
 * Handles communication with a single client in the Robot World server.
 * Parses incoming JSON commands and delegates them to CommandHandler for processing.
 */
public class ClientHandler implements Runnable {
    private final Socket clientSocket;
    private final World world;
    private final CommandHandler commandHandler;
    private static Boolean disconnected = false;
    private Robot robot;

    public ClientHandler(Socket socket, World world) {
        this.clientSocket = socket;
        this.world = world;
        this.commandHandler = new CommandHandler(world); // Initialize CommandHandler
    }

    @Override
    public void run() {
        String clientId = clientSocket.getRemoteSocketAddress().toString(); // Unique client ID

        try (
                BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
        ) {
            String message;
            while ((message = in.readLine()) != null) {
                System.out.println("Client [" + clientId + "]: " + message);
                JSONObject jsonObject;
                try {
                    jsonObject = new JSONObject(message);

                } catch (JSONException e) {
                    out.println("{\"error\": \"Invalid command format. Commands must be in JSON.\"}");
                    continue;
                }

                Command command;
                if(robot == null) {
                    JSONArray argsArray = jsonObject.optJSONArray("arguments");
                    String robotType = (argsArray != null && argsArray.length() > 0) ? argsArray.getString(0) : "";
                    robot = new Robot(jsonObject.getString("robot"), robotType);
                }
                try {
                    command = Command.fromJSON(jsonObject, robot);
                    System.out.println(command.robot.getMake());
                    System.out.println(command.robot.getName());
                    System.out.println("robot: "+robot.getX()+ " " + robot.getY());

                } catch (IllegalArgumentException e) {
                    Response response = new Response("ERROR", "Unsupported command");
                    System.out.println(response);
                    out.println(response.toJSONString());
                    out.flush();
                    continue;
                }

                synchronized (world) {
                    commandHandler.handle(command, clientId, response -> {
                        if (response.isOKResponse() && command.commandName().equals("launch")) {
                            Server.registerRobotHandler(command.robot.getName(), clientSocket);
                        }

                        this.robot = command.robot;

                        System.out.println("client handler response");
                        System.out.println(command.robot.getName());
                        System.out.println("robot: "+robot.getX()+ " " + robot.getY());
                        System.out.println(response.toJSONString());
                        out.println(response.toJSONString());
                        this.world.displayWorld();
                    });
                }
            }

        } catch (IOException e) {
            if (!disconnected) {
                System.out.println("Error handling client " + clientId + ": " + e);
            }
        } finally {
            try {
                clientSocket.close();
                System.out.println("\nClient disconnected: " + clientId);
            } catch (IOException e) {
                System.out.println("Error closing client socket: " + e);
            }
        }
    }

    public static void disconnect(String message, Socket socket, String robotName, PrintWriter out) {
        try {
            out.println(message);
            out.flush();
            socket.close();
            System.out.println("Connection closed for " + robotName + ".\n");
            disconnected = true;
            Server.unregisterRobotHandler(robotName);
        } catch (IOException e) {
            System.err.println("Error closing connection for " + robotName + ": " + e.getMessage()+ "\n");
        }
    }

}