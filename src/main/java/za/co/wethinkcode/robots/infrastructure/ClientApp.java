package za.co.wethinkcode.robots.infrastructure;

import org.json.JSONObject;
import za.co.wethinkcode.robots.domain.commands.Command;
import za.co.wethinkcode.robots.domain.responses.Response;

import java.io.*;
import java.net.Socket;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class ClientApp {
    private static final int MAX_ROBOTS = 2;
    private static final List<String> VALID_ROBOT_TYPES = List.of("sniper", "tank", "rover");
    private static volatile boolean isRunning = true;
    private static final BlockingQueue<String> responseQueue = new LinkedBlockingQueue<>();

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        Map<String, String> robots = new HashMap<>();
        String host = "localhost";
        int portNumber = promptInt(scanner, "Enter the port number:");

        try (
                Socket socket = new Socket(host, portNumber);
                PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
                BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))
        ) {
            while (isRunning) {
                System.out.println("Ready for launch!");
                while (robots.size() < MAX_ROBOTS) {
                    if (launchRobot(scanner, robots, out, in)) {
                        startServerListener(in);
                        handleCommands(scanner, robots.keySet().iterator().next(), out);
                        break;
                    }
                }
                if (robots.size() >= MAX_ROBOTS) {
                    System.out.println("ERROR: Cannot launch more than " + MAX_ROBOTS + " robots.");
                }
            }
        } catch (IOException e) {
            System.out.println("Connection failed: " + e.getMessage());
        }
    }

    private static boolean launchRobot(Scanner scanner, Map<String, String> robots, PrintWriter out, BufferedReader in) {
        System.out.println("Available robot makes: tank, sniper, rover");
        String launch = prompt(scanner, "Launch a robot (type 'launch <make> <name>'):");

        try {
            String[] parts = launch.split(" ");
            if (parts.length != 3 || !parts[0].equalsIgnoreCase("launch")) {
                throw new IllegalArgumentException("Invalid command format. Use: launch <make> <name>");
            }
            String robotName = parts[2].toLowerCase();
            String cmd = Command.fromInput(launch, robotName);
            System.out.println("Launching your robot into the world 🚀");
            out.println(cmd);

            String jsonString = in.readLine();
            JSONObject jsonObject = new JSONObject(jsonString);
            String response = Response.responseFromJSONString(jsonString);

            if (!jsonObject.has("state") || !jsonObject.getString("result").equalsIgnoreCase("OK")) {
                System.out.println(response + ". Please try again.");
                return false;
            }
            System.out.println("Robot launched successfully.");
            sleep(4000);
            System.out.println(response);
            robots.put(robotName, parts[1].toLowerCase());
            return true;
        } catch (IllegalArgumentException | IOException e) {
            System.out.println("Invalid Command. Please try again: " + e.getMessage());
            return false;
        }
    }

    private static void handleCommands(Scanner scanner, String robotName, PrintWriter out) {
        System.out.println("\nTo see available commands use: 'help'\n");
        while (isRunning) {
            String message = promptForCommand(scanner);
            if (isRestrictedCommand(message)) {
                System.out.println("This command can only be run by the server admin");
                continue;
            }
            if (!sendCommand(message, robotName, out)) {
                continue;
            }
            handleServerResponse(message);
        }
    }

    private static String promptForCommand(Scanner scanner) {
        System.out.print("Enter command: ");
        return scanner.nextLine();
    }

    private static boolean sendCommand(String message, String robotName, PrintWriter out) {
        try {
            String cmd = Command.fromInput(message, robotName);
            if (cmd.contains("Invalid command")) {
                System.out.println(cmd);
                return false;
            }
            if (cmd.contains("\"ERROR\"")) {
                System.out.println(new JSONObject(cmd).getString("ERROR"));
                return false;
            }
            out.println(cmd);
            return true;
        } catch (IllegalArgumentException e) {
            System.out.println("Invalid Command. Try again: " + e.getMessage());
            return false;
        }
    }

    private static void handleServerResponse(String message) {
        try {
            String response = responseQueue.take();
            JSONObject responseJson = new JSONObject(response);

            if (message.equalsIgnoreCase("help") && responseJson.has("data")) {
                printHelp(responseJson);
            } else {
                System.out.println("Server: \n" + response);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private static void printHelp(JSONObject responseJson) {
        JSONObject data = responseJson.getJSONObject("data");
        if (data.has("message")) {
            JSONObject messageData = new JSONObject(data.getString("message"));
            System.out.println("\nHere is a list of available commands\n");
            for (String key : messageData.keySet()) {
                System.out.println(key + " : " + messageData.getString(key) + "\n");
            }
        }
    }

    private static void startServerListener(BufferedReader in) {
        new Thread(() -> {
            try {
                String message;
                while ((message = in.readLine()) != null && isRunning) {
                    if (message.trim().startsWith("{")) {
                        responseQueue.offer(message);
                    } else {
                        System.out.println("\nServer: " + message);
                    }
                    String lowerMsg = message.toLowerCase();
                    if (lowerMsg.contains("purged") || lowerMsg.contains("dead") || lowerMsg.contains("removed")) {
                        System.out.println("Goodbye.");
                        isRunning = false;
                        System.exit(0);
                    }
                }
            } catch (IOException e) {
                System.out.println("Error reading from server: " + e.getMessage());
            }
        }, "ServerListener").start();
    }

    private static String prompt(Scanner scanner, String message) {
        System.out.println(message);
        return scanner.nextLine();
    }

    private static int promptInt(Scanner scanner, String message) {
        System.out.println(message);
        while (true) {
            String line = scanner.nextLine();
            try {
                return Integer.parseInt(line.trim());
            } catch (NumberFormatException e) {
                System.out.println("Please enter a valid number:");
            }
        }
    }

    private static boolean isRestrictedCommand(String message) {
        String lower = message.toLowerCase();
        return lower.equals("quit") || lower.equals("robots") || lower.equals("dump");
    }

    private static void sleep(int millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ignored) {}
    }
}
