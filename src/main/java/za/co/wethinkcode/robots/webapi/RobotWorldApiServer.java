package za.co.wethinkcode.robots.webapi;

import io.javalin.Javalin;
import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.database.WorldDatabase;

/**
 * Web API server for the RobotWorld application using Javalin.
 * Provides REST endpoints for world management and robot operations.
 */
public class RobotWorldApiServer {
    private final Javalin server;
    private final World world;
    private final WorldDatabase worldDatabase;

    public RobotWorldApiServer() {
        this.world = World.getInstance();
        this.worldDatabase = new WorldDatabase();
        
        server = Javalin.create();

        setupRoutes();
    }

    private void setupRoutes() {
        // GET /world - Get current world state
        server.get("/world", context -> RobotWorldApiHandler.getCurrentWorld(context, world));
        
        // GET /world/{name} - Get world by name from database
        server.get("/world/{name}", context -> RobotWorldApiHandler.getWorldByName(context, world, worldDatabase));
        
        // POST /robot/{name} - Execute robot command
        server.post("/robot/{name}", context -> RobotWorldApiHandler.executeRobotCommand(context, world));
    }

    public void start(int port) {
        this.server.start(port);
        System.out.println("Robot World API server started on port " + port);
    }

    public void stop() {
        this.server.stop();
        System.out.println("Robot World API server stopped");
    }

    public static void main(String[] args) {
        RobotWorldApiServer apiServer = new RobotWorldApiServer();
        int port = args.length > 0 ? Integer.parseInt(args[0]) : 8080;
        apiServer.start(port);
    }
}
