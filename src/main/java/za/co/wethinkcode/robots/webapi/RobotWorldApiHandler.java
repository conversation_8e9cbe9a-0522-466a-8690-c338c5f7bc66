package za.co.wethinkcode.robots.webapi;

import io.javalin.http.Context;
import io.javalin.http.BadRequestResponse;

import za.co.wethinkcode.robots.domain.world.World;
import za.co.wethinkcode.robots.domain.robot.Robot;
import za.co.wethinkcode.robots.domain.commands.LaunchCommand;
import za.co.wethinkcode.robots.domain.responses.Response;
import za.co.wethinkcode.robots.database.WorldDatabase;

import org.json.JSONObject;
import org.json.JSONArray;

import java.util.concurrent.atomic.AtomicReference;

/**
 * Handler class for processing Robot World API requests.
 */
public class RobotWorldApiHandler {

    /**
     * Get the current world state as JSON
     */
    public static void getCurrentWorld(Context context, World world) {
        try {
            JSONObject worldJson = createWorldJson(world);
            context.json(worldJson.toString());
        } catch (Exception e) {
            context.status(500);
            context.result("Error retrieving world state: " + e.getMessage());
        }
    }

    /**
     * Get world by name from database and restore it
     */
    public static void getWorldByName(Context context, World world, WorldDatabase worldDatabase) {
        try {
            String worldName = context.pathParam("name");

            // Restore world from database
            worldDatabase.restoreWorld(worldName, world);

            JSONObject worldJson = createWorldJson(world);
            context.json(worldJson.toString());
        } catch (Exception e) {
            context.status(404);
            context.result("World not found: " + e.getMessage());
        }
    }

    /**
     * Execute a robot command
     */
    public static void executeRobotCommand(Context context, World world) {
        try {
            String robotName = context.pathParam("name");

            // Parse the JSON command from request body
            JSONObject commandJson = new JSONObject(context.body());

            // Validate required fields
            if (!commandJson.has("command") || !commandJson.has("arguments")) {
                throw new BadRequestResponse("Missing required fields: command and arguments");
            }

            String commandName = commandJson.getString("command");
            JSONArray argumentsArray = commandJson.getJSONArray("arguments");

            // For now, only support Launch command
            if (!"launch".equalsIgnoreCase(commandName)) {
                throw new BadRequestResponse("Only 'launch' command is currently supported");
            }

            // Extract launch command arguments
            if (argumentsArray.length() < 1) {
                throw new BadRequestResponse("Launch command requires at least 1 argument: [kind]");
            }

            String kind = argumentsArray.getString(0);

            // Create robot and launch command
            Robot robot = new Robot(robotName, kind);
            LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{kind});

            // Execute command using World.execute method
            AtomicReference<Response> responseRef = new AtomicReference<>();
            world.execute(launchCommand, "web-api-client", responseRef::set);

            Response response = responseRef.get();

            // Convert response to JSON and return
            context.status(201);
            context.json(response.toJSONString());

        } catch (BadRequestResponse e) {
            throw e;
        } catch (Exception e) {
            context.status(500);
            context.result("Error executing command: " + e.getMessage());
        }
    }

    /**
     * Create JSON representation of the world state
     */
    private static JSONObject createWorldJson(World world) {
        JSONObject worldJson = new JSONObject();

        // Add world dimensions
        worldJson.put("width", world.getWidth());
        worldJson.put("height", world.getHeight());

        // Add obstacles
        JSONArray obstaclesArray = new JSONArray();
        for (var obstacle : world.getObstacles()) {
            JSONObject obstacleJson = new JSONObject();
            obstacleJson.put("x", obstacle.x());
            obstacleJson.put("y", obstacle.y());
            obstacleJson.put("width", obstacle.width());
            obstacleJson.put("height", obstacle.height());
            obstacleJson.put("type", obstacle.type().toString());
            obstaclesArray.put(obstacleJson);
        }
        worldJson.put("obstacles", obstaclesArray);

        // Add robots
        JSONArray robotsArray = new JSONArray();
        for (Robot robot : world.getRobots()) {
            JSONObject robotJson = new JSONObject();
            robotJson.put("name", robot.getName());
            robotJson.put("make", robot.getMake());
            robotJson.put("x", robot.getPosition().getX());
            robotJson.put("y", robot.getPosition().getY());
            robotJson.put("direction", robot.getDirection().toString());
            robotJson.put("shields", robot.getShields());
            robotJson.put("shots", robot.getShots());
            robotJson.put("status", robot.status.toString());
            robotsArray.put(robotJson);
        }
        worldJson.put("robots", robotsArray);

        return worldJson;
    }

}
