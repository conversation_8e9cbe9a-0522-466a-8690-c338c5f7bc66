Scenario: Save a World

Given a configured world with size and obstacles,
When the admin types save my_world,
Then the system stores the world in the database,
And confirms with "World saved as 'my_world'".

Scenario: Restore a World

Given a previously saved world named my_world,
When the admin types restore my_world,
Then the current world is replaced with the restored one,
And the system confirms with "World 'my_world' restored."

Scenario: Save Existing World Name

Given a world named my_world already exists,
When the admin types save my_world,
Then the system prompts: World 'my_world' exists. Overwrite? (y/n):
And saves the world only if y is entered.

Scenario: Restore Non-Existent World

When the admin types restore ghost_world,
And ghost_world doesn’t exist,
Then the system prints: World 'ghost_world' does not exist.