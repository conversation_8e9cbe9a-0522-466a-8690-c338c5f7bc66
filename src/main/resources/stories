Robot world stories

Story 1
TITLE: Launch a Robot

As a client
I want to launch my robot in the robot world
So that I can participate in the robot world activities

Scenarios

Scenario 1.a
TITLE: Valid launch command should succeed

Given that I am connected to a Robot Worlds server
And the world has available space
When I send a valid launch request with robot name "<PERSON><PERSON><PERSON>"
Then I should get a successful response from the server (the response should be "OK")
And the robot should be placed at a valid position (I should receive the initial position (x,y co-ordinates))
And I should receive the initial state of my robot

Scenario 1.b
TITLE: Incorrect launch command should fail

Given that I am connected to a Robot Worlds server
When I send an invalid launch request with command "luanch" instead of "launch"
Then I should get an "ERROR" response
And the message should be "Unsupported command"

Scenario 1.c
TITLE: The world has no space for a robot

Given that I am connected to a Robot Worlds server
And the world is at maximum capacity with robots
When I send a valid launch request
Then I should get an "ERROR" response
And the message should be "No more space in this world"

Scenario 1.d
TITLE: The world cannot have robots with the same names

Given that I am connected to a Robot Worlds server
And there is already a robot named "<PERSON><PERSON><PERSON>" in the world
When I send a launch request with name "<PERSON><PERSON><PERSON>"
Then I should get an "ERROR" response
And the message should be "Too many of you in this world"

Scenario 1.e
TITLE: Multiple Robot Support

Given that I am connected to a running Robot Worlds server
And I have already launched one robot named "Mbali"
When I launch another robot named "<PERSON>"
Then the launch should be successful
And both robots should be active in the world

Scenario 1.f
TITLE: World Capacity Management

Given that I am connected to a running Robot Worlds server
And the world has no obstacles
And the world already contains the maximum number of robots
When I try to launch another robot
Then the launch should fail
And I should get an error message "No more space in this world"

Scenario 1.g
TITLE: Launch robots into a world with obstacles

Given that I am connected to a running Robot Worlds server
And the world contains obstacles
When I launch a robot
Then the launch should be successful
And when the robot looks around
Then it should detect at least one obstacle

Scenario 1.h
TITLE : World with obstacles is at maximum capacity

Given that I am connected to a running Robot Worlds server
And the world contains obstacles
And the available spaces are occupied by robots
When I try to launch another robot
Then the launch should fail
And I should get an error message "No more space in this world"
And the number of robots should be less than in a world without obstacles

Acceptance Criteria:
- The robot should be placed in an available position
- The robot should have a unique name
- The world should have space for the robot
- The launch command should be correctly formatted

Notes:
- All scenarios assume a successful connection to the server
- The world size and maximum capacity are defined by the server configuration
- Robot names must be unique across the entire world
- Initial position is assigned by the server based on available space


Story 2: Get the State of the Robot

As a client
I want to get the current state of my robot
So that I can know its position, shield strength, shots, direction and status

Acceptance Criteria:
- The robot must exist in the world
- The state request should return all robot properties
- Invalid robot requests should return appropriate errors

Scenario 2.a
TITLE: State for valid robot
Given I am connected to the Robot World server
And I have a robot named "Mbali" in the world
When I send a state request for "Mbali"
Then the response should be "OK"
And I should receive a state object containing:
  - Position coordinates (x:2, y:3)
  - Current direction: "NORTH"
  - Shields: 3
  - Shots: 5
  - Status: "NORMAL"

Scenario 2.b
TITLE: Error for invalid robot
Given I am connected to the Robot World server
And there is no robot named "UNKNOWN-ROBOT" in the world
When I send a state request for "UNKNOWN-ROBOT"
Then the response should be "ERROR"
And I should receive the message "Robot does not exist"

Notes:
- The state request must include the robot's name
- State information is only available for active robots
- Shield strength is represented as an integer value
- Position coordinates are represented as integer values
- Direction can be one of: "NORTH", "SOUTH", "EAST", "WEST"
- Status can be: "NORMAL", "DEAD", or "REPAIRING"



Story 3: Look Around

As a client
I want my robot to look around in the world
So that I can identify objects and other robots in its vicinity

Acceptance Criteria:
- The robot should be able to see objects in adjacent positions
- The robot should be able to see other robots in adjacent positions
- The robot should receive information about empty spaces
- The look command should work in all four directions

Scenario 3.a
TITLE: Empty World View

Given I have a robot in the world at position (x:2, y:2)
And all adjacent positions are empty
When I send a look command
Then the response should be "OK"
And I should receive:
  - NORTH: "EMPTY"
  - EAST: "EMPTY"
  - SOUTH: "EMPTY"
  - WEST: "EMPTY"

Scenario 3.b
TITLE: World with Obstacles

Given I have a robot in the world at position (x:2, y:2)
And there is an obstacle at position (x:2, y:3)
When I send a look command
Then the response should be "OK"
And I should receive:
  - NORTH: "OBSTACLE"
  - EAST: "EMPTY"
  - SOUTH: "EMPTY"
  - WEST: "EMPTY"

Scenario 3.c
TITLE: World with another robot

Given I have a robot in the world at position (x:2, y:2)
And there is another robot at position (x:3, y:2)
When I send a look command
Then the response should be "OK"
And I should receive:
  - NORTH: "EMPTY"
  - EAST: "ROBOT"
  - SOUTH: "EMPTY"
  - WEST: "EMPTY"


Scenario 3.d
TITLE: See robots and obstacles in world

Given I have a robot in the world at position (x:2, y:2)
And there is an obstacle at position (x:2, y:3)
When I send a look command
Then the response should be "OK"
And I should receive:
  - NORTH: "OBSTACLE"
  - EAST: "ROBOT"
  - SOUTH: "EMPTY"
  - WEST: "EMPTY"


Notes:
- The look command provides visibility in all four directions
- Objects detected can be: "EMPTY", "OBSTACLE", "ROBOT", or "EDGE"
- The look command only shows immediate adjacent positions
- World boundaries are treated as "EDGE" objects



Story 4: Move Robot

As a client
I want to move my robot in different directions
So that I can navigate through the world and complete objectives

Acceptance Criteria:
- The robot should move in the specified direction when path is clear
- The robot should not move through obstacles
- The robot should not move through other robots
- The robot should not move beyond world boundaries

Scenario 4.a
TITLE: Successful Movement in Clear Path

Given I have a robot at position (x:2, y:2)
And the path to the north is clear
When I send a move command with direction "NORTH"
Then the response should be "OK"
And the robot should be at position (x:2, y:3)
And I should receive updated state information

Scenario 4.b
Title: Movement Blocked by Obstacle

Given I have a robot at position (x:2, y:2)
And there is an obstacle at position (x:2, y:3)
When I send a move command with direction "NORTH"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by obstacle"
And the robot should remain at position (x:2, y:2)

Scenario 4.c
Title: Movement blocked by another robot

Given I have a robot at position (x:2, y:2)
And there is another robot at position (x:2, y:3)
When I send a move command with direction "NORTH"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by robot"
And the robot should remain at position (x:2, y:2)

Scenario 4.d
Title: Movement at world boundary

Given I have a robot at position (x:0, y:0)
And the robot is at the world's edge
When I send a move command with direction "SOUTH"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by world boundary"
And the robot should remain at position (x:0, y:0)

Notes:
- Valid movement directions are: NORTH, SOUTH, EAST, WEST
- Each movement changes one coordinate by 1 unit
- The world has defined boundaries (0,0) to (max_x, max_y)
- Robots cannot occupy the same position
- Movement is atomic - either succeeds completely or fails


Story 5: Fire Weapon

As a client
I want my robot to fire its weapon
So that I can engage in combat with other robots

Acceptance Criteria:
- The robot should be able to fire in any of the four directions
- The weapon should affect the first target it hits
- The weapon should have limited ammunition
- Hits should reduce target shields

Scenario 5.a
Title: Successful Hit on Enemy Robot

Given I have a robot with ammunition
And there is an enemy robot at position (x:2, y:3)
And the enemy robot has 3 shields
When I fire in direction "NORTH"
Then the response should be "OK"
And the enemy robot's shields should decrease by 1
And my ammunition should decrease by 1
And I should receive a hit confirmation

Scenario 5.b
Title: Missed Shot with no target

Given I have a robot with ammunition
And there are no targets in firing line
When I fire in direction "EAST"
Then the response should be "OK"
And my ammunition should decrease by 1
And I should receive a message "Miss"

Scenario 5.c

Title: Attempt to fire without ammunition

Given I have a robot with 0 ammunition
When I attempt to fire in any direction
Then the response should be "ERROR"
And I should receive the message "No ammunition"
And the weapon should not fire

Scenario 5.d
Title: Shot blocked by obstacle

Given I have a robot with ammunition
And there is an obstacle at position (x:2, y:3)
And there is an enemy robot at position (x:2, y:4)
When I fire in direction "NORTH"
Then the response should be "OK"
And my ammunition should decrease by 1
And the enemy robot should not be damaged
And I should receive a message "Hit obstacle"

Scenario 5.e
Title: Robot Destruction on final shield

Given I have a robot with ammunition
And there is an enemy robot with 1 shield remaining
When I fire and hit the enemy robot
Then the response should be "OK"
And the enemy robot's status should change to "DEAD"
And I should receive a message "Robot destroyed"

Notes:
- Firing directions: NORTH, SOUTH, EAST, WEST
- Each shot consumes 1 ammunition
- Shields are reduced by 1 for each hit
- A robot with 0 shields is destroyed
- Obstacles block shots
- Maximum ammunition capacity is 10

Story 6: Forward Command

As a client
I want my robot to move forward multiple steps
So that I can navigate longer distances efficiently

Acceptance Criteria:
- The robot should move the specified number of steps in its current direction
- The robot should stop and report if any obstacle is encountered
- The robot should not move through world boundaries
- The robot should not collide with other robots
- The movement should be atomic - either complete all steps or none

Scenario 6.a
TITLE: Successful Forward Movement

Given I have a robot at position (x:2, y:2) facing NORTH
And the path ahead is clear for 3 steps
When I send a forward command with steps "3"
Then the response should be "OK"
And the robot should be at position (x:2, y:5)
And I should receive updated state information

Scenario 6.b
TITLE: Forward Movement Blocked by Obstacle

Given I have a robot at position (x:2, y:2) facing EAST
And there is an obstacle at position (x:4, y:2)
When I send a forward command with steps "3"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by obstacle at step 2"
And the robot should remain at position (x:2, y:2)

Scenario 6.c
TITLE: Forward Movement Blocked by Robot

Given I have a robot at position (x:2, y:2) facing NORTH
And there is another robot at position (x:2, y:4)
When I send a forward command with steps "3"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by robot at step 2"
And the robot should remain at position (x:2, y:2)

Scenario 6.d
TITLE: Forward Movement at World Boundary

Given I have a robot at position (x:8, y:8) facing NORTH
And the world boundary is at y=10
When I send a forward command with steps "3"
Then the response should be "ERROR"
And I should receive the message "Movement blocked by world boundary at step 2"
And the robot should remain at position (x:8, y:8)

Scenario 6.e
TITLE: Invalid Steps Parameter

Given I have a robot in the world
When I send a forward command with steps "-1"
Then the response should be "ERROR"
And I should receive the message "Invalid number of steps"
And the robot should not move

Notes:
- Forward movement is relative to the robot's current direction
- Steps must be a positive integer
- The robot maintains its original direction after movement
- Position coordinates are represented as integer values
- The world has defined boundaries (0,0) to (max_x, max_y)


