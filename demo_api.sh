#!/bin/bash

# Demo script for Robot World Web API
# This script demonstrates the basic functionality of the Web API

echo "=== Robot World Web API Demo ==="
echo

# Start the API server in the background
echo "Starting Robot World API server..."
java -cp target/classes:target/dependency/* za.co.wethinkcode.robots.webapi.RobotWorldApiServer 8080 &
API_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 3

echo "=== Testing API Endpoints ==="
echo

# Test 1: Get current world state
echo "1. GET /world - Get current world state:"
curl -s -X GET "http://localhost:8080/world" | python3 -m json.tool
echo
echo

# Test 2: Launch a robot
echo "2. POST /robot/Tank1 - Launch a tank robot:"
curl -s -X POST "http://localhost:8080/robot/Tank1" \
  -H "Content-Type: application/json" \
  -d '{"command":"launch","arguments":["tank"]}' | python3 -m json.tool
echo
echo

# Test 3: Launch another robot
echo "3. POST /robot/Sniper1 - Launch a sniper robot:"
curl -s -X POST "http://localhost:8080/robot/Sniper1" \
  -H "Content-Type: application/json" \
  -d '{"command":"launch","arguments":["sniper"]}' | python3 -m json.tool
echo
echo

# Test 4: Get world state again to see the robots
echo "4. GET /world - Get world state with robots:"
curl -s -X GET "http://localhost:8080/world" | python3 -m json.tool
echo
echo

# Test 5: Try to launch robot with invalid command
echo "5. POST /robot/Invalid - Try invalid command (should fail):"
curl -s -X POST "http://localhost:8080/robot/Invalid" \
  -H "Content-Type: application/json" \
  -d '{"command":"invalid","arguments":[]}' -w "HTTP Status: %{http_code}\n"
echo
echo

# Test 6: Try to launch robot with missing fields
echo "6. POST /robot/Missing - Try missing fields (should fail):"
curl -s -X POST "http://localhost:8080/robot/Missing" \
  -H "Content-Type: application/json" \
  -d '{"command":"launch"}' -w "HTTP Status: %{http_code}\n"
echo
echo

echo "=== Demo Complete ==="
echo "Stopping API server..."

# Stop the API server
kill $API_PID
wait $API_PID 2>/dev/null

echo "API server stopped."
echo
echo "The Robot World Web API supports:"
echo "- GET /world: Get current world state"
echo "- GET /world/{name}: Get world by name from database (if implemented)"
echo "- POST /robot/{name}: Execute robot commands (currently supports 'launch')"
echo
echo "Example launch command JSON:"
echo '{"command":"launch","arguments":["tank"]}'
echo
echo "Supported robot types: tank, sniper, rover"
