#!/bin/bash

# Build script that automatically sets up Java and Maven environment
# Usage: ./build.sh [make target]
# Example: ./build.sh build
# Example: ./build.sh test
# Example: ./build.sh clean

# Source the Java and Maven setup
source ./setup-java-maven.sh > /dev/null 2>&1

# If no arguments provided, run default build
if [ $# -eq 0 ]; then
    echo "Running default build..."
    make build
else
    # Run make with provided arguments
    make "$@"
fi
